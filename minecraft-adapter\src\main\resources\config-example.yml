# Voice Chat Adapter 配置文件
# 此文件配置 Minecraft 服务器与独立语音服务器的连接

# 语音服务器连接配置
voice-server:
  host: "localhost"
  port: 24454
  api-endpoint: "http://localhost:8080"
  auth-token: "your-auth-token-here"

# 同步配置
sync:
  enabled: true
  interval: 5000  # 毫秒
  sync-on-move: true
  min-move-distance: 1.0

# 服务器名称（用于跨服务器识别）
server-name: "survival"

# 跨服务器配置
cross-server:
  enabled: true
  allowed-servers: []  # 空列表表示允许所有服务器

# 跨世界配置
cross-world:
  enabled: true
  allowed-worlds: []  # 空列表表示允许所有世界

# 权限默认配置
permissions:
  defaults:
    listen: true
    speak: true
    groups: true
    create-group: true
    join-group: true
    cross-world: true
    cross-server: true
    record: false
    admin: false

# 世界配置
worlds:
  # 默认世界配置（适用于所有未特别配置的世界）
  default:
    voice-enabled: true
    max-voice-distance: 48.0
    groups-enabled: true
    cross-world-allowed: false
  
  # 特定世界配置
  specific:
    # 主世界配置
    world:
      voice-enabled: true
      max-voice-distance: 48.0
      groups-enabled: true
      cross-world-allowed: false
    
    # 地狱配置（语音距离更短）
    world_nether:
      voice-enabled: true
      max-voice-distance: 24.0
      groups-enabled: true
      cross-world-allowed: false
    
    # 末地配置（语音距离更远）
    world_the_end:
      voice-enabled: true
      max-voice-distance: 64.0
      groups-enabled: true
      cross-world-allowed: false
    
    # PvP 世界配置（禁用语音）
    world_pvp:
      voice-enabled: false
      max-voice-distance: 0.0
      groups-enabled: false
      cross-world-allowed: false
    
    # 创造世界配置（允许跨世界语音）
    world_creative:
      voice-enabled: true
      max-voice-distance: 96.0
      groups-enabled: true
      cross-world-allowed: true

# 配置说明：
# voice-enabled: 是否在该世界启用语音聊天
# max-voice-distance: 最大语音收听距离（方块）
# groups-enabled: 是否允许在该世界使用群组语音
# cross-world-allowed: 是否允许该世界的玩家与其他世界的玩家进行语音通信
