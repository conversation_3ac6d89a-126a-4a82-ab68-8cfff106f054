package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.model.Position;
import de.maxhenkel.voicechat.standalone.model.Permission;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 玩家API处理器
 */
public class PlayerApiHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerApiHandler.class);
    
    private final VoiceServer voiceServer;
    
    public PlayerApiHandler(VoiceServer voiceServer) {
        this.voiceServer = voiceServer;
    }
    
    /**
     * 处理玩家登录
     * POST /api/players/login
     */
    public void handlePlayerLogin(Context ctx) {
        try {
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            
            String uuidStr = (String) request.get("uuid");
            String name = (String) request.get("name");
            String serverName = (String) request.get("serverName");
            List<String> permissionNodes = (List<String>) request.get("permissions");
            
            if (uuidStr == null || name == null || serverName == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Missing required fields: uuid, name, serverName"));
                return;
            }
            
            UUID uuid = UUID.fromString(uuidStr);
            
            // 创建玩家数据
            PlayerData player = new PlayerData(uuid, name, serverName);
            
            // 设置权限
            if (permissionNodes != null) {
                Set<Permission> permissions = permissionNodes.stream()
                        .map(Permission::fromNode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                player.setPermissions(permissions);
            }
            
            // 设置位置（如果提供）
            Map<String, Object> positionData = (Map<String, Object>) request.get("position");
            if (positionData != null) {
                String worldId = (String) positionData.get("worldId");
                Position position = new Position(
                        worldId,
                        ((Number) positionData.get("x")).doubleValue(),
                        ((Number) positionData.get("y")).doubleValue(),
                        ((Number) positionData.get("z")).doubleValue()
                );
                player.setPosition(position);

                // 同时设置worldName字段
                player.setWorldName(worldId);
            }
            
            // 添加到玩家管理器
            voiceServer.getPlayerManager().addPlayer(player);
            
            LOGGER.info("Player {} logged in from server {}", name, serverName);
            
            ctx.status(HttpStatus.OK)
               .json(Map.of(
                   "success", true,
                   "message", "Player logged in successfully",
                   "player", playerToMap(player)
               ));
               
        } catch (Exception e) {
            LOGGER.error("Error handling player login", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 处理玩家登出
     * POST /api/players/logout
     */
    public void handlePlayerLogout(Context ctx) {
        try {
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            String uuidStr = (String) request.get("uuid");
            String serverName = (String) request.get("serverName"); // 新增：指定从哪个服务器登出

            if (uuidStr == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "Missing required field: uuid"));
                return;
            }

            UUID uuid = UUID.fromString(uuidStr);
            PlayerData player = voiceServer.getPlayerManager().getPlayer(uuid);

            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }

            // 检查是否是从正确的服务器登出
            if (serverName != null && !serverName.equals(player.getServerName())) {
                LOGGER.warn("Player {} logout request from server {} but player is on server {}",
                           player.getName(), serverName, player.getServerName());
                // 仍然处理登出，但记录警告
            }

            // 断开语音连接
            voiceServer.disconnectPlayer(uuid);

            // 从群组中移除玩家
            if (player.hasGroup()) {
                voiceServer.getGroupManager().leaveGroup(player.getGroupId(), uuid);
            }

            // 设置玩家离线并清理状态
            voiceServer.getPlayerManager().setPlayerOnline(uuid, false);

            LOGGER.info("Player {} logged out from server {}", player.getName(),
                       serverName != null ? serverName : player.getServerName());

            ctx.status(HttpStatus.OK)
               .json(Map.of("success", true, "message", "Player logged out successfully"));

        } catch (Exception e) {
            LOGGER.error("Error handling player logout", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 处理位置更新
     * PUT /api/players/{uuid}/position
     */
    public void handleUpdatePosition(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID uuid = UUID.fromString(uuidStr);
            
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            String worldId = (String) request.get("worldId");
            double x = ((Number) request.get("x")).doubleValue();
            double y = ((Number) request.get("y")).doubleValue();
            double z = ((Number) request.get("z")).doubleValue();
            
            Position position = new Position(worldId, x, y, z);
            voiceServer.getPlayerManager().updatePlayerPosition(uuid, position);
            
            ctx.status(HttpStatus.OK)
               .json(Map.of("success", true, "message", "Position updated successfully"));
               
        } catch (Exception e) {
            LOGGER.error("Error updating player position", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 处理权限更新
     * PUT /api/players/{uuid}/permissions
     */
    public void handleUpdatePermissions(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID uuid = UUID.fromString(uuidStr);
            
            Map<String, Object> request = ctx.bodyAsClass(Map.class);
            List<String> permissionNodes = (List<String>) request.get("permissions");
            
            Set<Permission> permissions = permissionNodes.stream()
                    .map(Permission::fromNode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            voiceServer.getPlayerManager().updatePlayerPermissions(uuid, permissions);
            
            ctx.status(HttpStatus.OK)
               .json(Map.of("success", true, "message", "Permissions updated successfully"));
               
        } catch (Exception e) {
            LOGGER.error("Error updating player permissions", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取玩家状态
     * GET /api/players/{uuid}/state
     */
    public void handleGetPlayerState(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID uuid = UUID.fromString(uuidStr);
            
            PlayerData player = voiceServer.getPlayerManager().getPlayer(uuid);
            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }
            
            ctx.json(playerToMap(player));
            
        } catch (Exception e) {
            LOGGER.error("Error getting player state", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有玩家
     * GET /api/players
     */
    public void handleGetAllPlayers(Context ctx) {
        try {
            Collection<PlayerData> players = voiceServer.getPlayerManager().getAllPlayers();
            List<Map<String, Object>> playerList = players.stream()
                    .map(this::playerToMap)
                    .collect(Collectors.toList());
            
            ctx.json(Map.of(
                "players", playerList,
                "count", playerList.size()
            ));
            
        } catch (Exception e) {
            LOGGER.error("Error getting all players", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }
    
    /**
     * 生成玩家认证密钥
     * POST /api/players/{uuid}/secret
     */
    public void handleGenerateSecret(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID uuid = UUID.fromString(uuidStr);
            
            PlayerData player = voiceServer.getPlayerManager().getPlayer(uuid);
            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }
            
            UUID secret = voiceServer.getConnectionManager().generateSecret(uuid);
            
            ctx.json(Map.of(
                "success", true,
                "secret", secret.toString(),
                "playerUuid", uuid.toString()
            ));
            
        } catch (Exception e) {
            LOGGER.error("Error generating player secret", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
    
    /**
     * 将玩家数据转换为Map
     */
    private Map<String, Object> playerToMap(PlayerData player) {
        Map<String, Object> map = new HashMap<>();
        map.put("uuid", player.getUuid().toString());
        map.put("name", player.getName());
        map.put("online", player.isOnline());
        map.put("voiceConnected", player.isVoiceConnected());
        map.put("voiceDisabled", player.isVoiceDisabled());
        map.put("serverName", player.getServerName());
        map.put("lastUpdate", player.getLastUpdate());
        
        if (player.getPosition() != null) {
            Position pos = player.getPosition();
            map.put("position", Map.of(
                "worldId", pos.getWorldId(),
                "x", pos.getX(),
                "y", pos.getY(),
                "z", pos.getZ(),
                "timestamp", pos.getTimestamp()
            ));
        }
        
        if (player.getGroupId() != null) {
            map.put("groupId", player.getGroupId().toString());
        }
        
        map.put("permissions", player.getPermissions().stream()
                .map(Permission::getPermissionNode)
                .collect(Collectors.toList()));
        
        return map;
    }

    /**
     * 获取所有玩家的连接状态
     * GET /api/players/connections
     */
    public void handleGetPlayerConnections(Context ctx) {
        try {
            Map<String, Boolean> connectionStates = new HashMap<>();

            // 获取所有已知玩家的连接状态
            for (PlayerData player : voiceServer.getPlayerManager().getAllPlayers()) {
                UUID playerUuid = player.getUuid();
                boolean isConnected = voiceServer.isPlayerConnected(playerUuid);
                connectionStates.put(playerUuid.toString(), isConnected);
            }

            ctx.json(connectionStates);

        } catch (Exception e) {
            LOGGER.error("Failed to get player connections", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Failed to get player connections: " + e.getMessage()));
        }
    }

    /**
     * 获取所有服务器的在线玩家信息（包括群组信息）
     * GET /api/players/online/all
     */
    public void handleGetAllOnlinePlayers(Context ctx) {
        try {
            Collection<PlayerData> allPlayers = voiceServer.getPlayerManager().getAllPlayers();
            List<Map<String, Object>> onlinePlayersList = new ArrayList<>();

            for (PlayerData player : allPlayers) {
                if (player.isOnline()) {
                    Map<String, Object> playerInfo = new HashMap<>();
                    playerInfo.put("uuid", player.getUuid().toString());
                    playerInfo.put("name", player.getName());
                    playerInfo.put("serverName", player.getServerName());
                    playerInfo.put("connected", voiceServer.isPlayerConnected(player.getUuid()));

                    // 添加群组信息
                    if (player.getGroupId() != null) {
                        playerInfo.put("groupId", player.getGroupId().toString());
                    }

                    // 添加位置信息
                    if (player.getPosition() != null) {
                        Position pos = player.getPosition();
                        playerInfo.put("position", Map.of(
                            "worldId", pos.getWorldId(),
                            "x", pos.getX(),
                            "y", pos.getY(),
                            "z", pos.getZ()
                        ));
                    }

                    onlinePlayersList.add(playerInfo);
                }
            }

            ctx.json(Map.of(
                "players", onlinePlayersList,
                "count", onlinePlayersList.size()
            ));

        } catch (Exception e) {
            LOGGER.error("Failed to get all online players", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Failed to get all online players: " + e.getMessage()));
        }
    }
}
