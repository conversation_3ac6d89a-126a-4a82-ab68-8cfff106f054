# 跨服务器连接优化指南

## 问题描述

在多服务器环境中，跨服务器语音通信需要等待一段时间才能建立连接。这是因为：

1. **服务器注册是异步的**：每个服务器独立注册到独立语音服务器
2. **跨服务器规则创建延迟**：只有当两个服务器都注册后才创建通信规则
3. **Keepalive 失败导致重新注册**：网络问题可能导致服务器需要重新注册

## 优化措施

### 1. 减少 Keepalive 间隔

**独立语音服务器配置** (`voice-server.yml`)：
```yaml
cross-server:
  # Keepalive超时时间 - 从30分钟减少到5分钟
  keepalive_timeout: 300000   # 5分钟
  # 清理间隔 - 从30分钟减少到2分钟
  cleanup_interval: 120000    # 2分钟
  # 立即建立跨服务器规则
  immediate_rule_establishment: true
```

**Adapter 配置**：
- Keepalive 间隔从5分钟减少到2分钟
- 更快检测连接问题并重新注册

### 2. 立即建立跨服务器规则

修改了 `ServerRegistrationManager.registerServer()` 方法：
- 新服务器注册时立即为所有现有服务器建立双向通信规则
- 避免等待第二个服务器注册才创建规则

### 3. 智能重新注册

改进了重新注册逻辑：
- 允许服务器重新注册（不再拒绝已存在的服务器名）
- 重新注册后立即同步所有在线玩家
- 自动重建跨服务器连接

### 4. 快速故障恢复

添加了快速恢复机制：
- 检测到连接问题时立即尝试重新连接
- 重新连接成功后立即同步玩家状态
- 减少服务器重启后的恢复时间

## 预期效果

### 优化前
```
服务器A启动 -> 注册成功
服务器B启动 -> 注册成功 -> 等待30秒-5分钟 -> 跨服务器通信可用
```

### 优化后
```
服务器A启动 -> 注册成功
服务器B启动 -> 注册成功 -> 立即建立规则 -> 跨服务器通信可用（<10秒）
```

## 配置建议

### 1. 独立语音服务器配置

```yaml
# voice-server.yml
cross-server:
  auto_enable: true
  default_allowed: true
  registration_timeout: 30000
  keepalive_timeout: 300000    # 5分钟
  cleanup_interval: 120000     # 2分钟
  immediate_rule_establishment: true
```

### 2. Adapter 配置

```yaml
# config.yml
sync:
  enabled: true
  interval: 3000  # 减少到3秒以提高响应速度
  sync-on-move: true
  min-move-distance: 1.0

cross-server:
  enabled: true
  allowed-servers: []  # 允许所有服务器
```

### 3. 服务器启动顺序

**推荐启动顺序**：
1. 启动独立语音服务器
2. 等待2-3秒
3. 同时启动所有 Minecraft 服务器

**不推荐**：
- 服务器间隔太长时间启动
- 频繁重启单个服务器

## 监控和诊断

### 1. 关键日志信息

**独立语音服务器**：
```
[INFO] Server 'serverA' registered successfully
[INFO] Established X cross-server rules for Y servers
[INFO] Added cross-server rule: serverA -> serverB
```

**Adapter**：
```
[INFO] Successfully re-registered server: serverA
[INFO] Resyncing X online players after reconnect
[INFO] Completed resyncing all players after reconnect
```

### 2. 故障排除

**如果跨服务器通信仍然延迟**：

1. **检查网络连接**：
   ```bash
   # 测试 Adapter 到独立语音服务器的连接
   curl -X GET http://localhost:8080/api/health
   ```

2. **检查服务器注册状态**：
   ```bash
   # 查看已注册的服务器
   curl -X GET http://localhost:8080/api/servers
   ```

3. **检查跨服务器规则**：
   ```bash
   # 查看跨服务器规则
   curl -X GET http://localhost:8080/api/cross-server/rules
   ```

4. **重启顺序**：
   - 先重启独立语音服务器
   - 等待5秒
   - 重启所有 Minecraft 服务器

### 3. 性能监控

**监控指标**：
- 服务器注册时间
- 跨服务器规则建立时间
- Keepalive 成功率
- 玩家同步时间

**正常指标**：
- 服务器注册：< 5秒
- 跨服务器规则建立：< 10秒
- Keepalive 成功率：> 95%
- 玩家同步：< 3秒

## 高级优化

### 1. 预热连接

在服务器启动脚本中添加预热步骤：
```bash
#!/bin/bash
# 启动独立语音服务器
java -jar voice-server.jar &
sleep 5

# 启动所有 Minecraft 服务器
java -jar server1.jar &
java -jar server2.jar &
wait
```

### 2. 健康检查

添加健康检查脚本：
```bash
#!/bin/bash
# 检查跨服务器连接状态
response=$(curl -s http://localhost:8080/api/cross-server/status)
if [[ $response == *"healthy"* ]]; then
    echo "Cross-server communication is healthy"
else
    echo "Cross-server communication issues detected"
    # 触发重启或告警
fi
```

通过这些优化措施，跨服务器连接等待时间应该从几分钟减少到几秒钟。
