# Voice Chat Adapter Configuration

# 语音服务器配置
voice-server:
  # 语音服务器地址
  host: "localhost"
  # 语音服务器UDP端口
  port: 24454
  # API端点地址
  api-endpoint: "http://localhost:8080"
  # 认证令牌（必须与语音服务器配置一致）
  auth-token: "change-this-secret-token"

# 同步配置
sync:
  # 位置同步间隔（毫秒），0表示禁用定时同步
  position-interval: 1000
  # 权限同步间隔（毫秒），0表示禁用定时同步
  permission-interval: 5000
  # 是否在玩家移动时立即同步位置
  sync-on-move: true
  # 最小移动距离（方块），小于此距离的移动不会触发同步
  min-move-distance: 1.0

# 服务器名称（用于区分不同的Minecraft服务器）
# 重要：此名称必须在所有连接到同一独立语音服务器的Adapter中保持唯一
# 如果名称冲突，插件将拒绝启动
server-name: "default"

# 跨服务器通信配置
cross-server:
  # 是否启用跨服务器通信（允许与其他服务器的玩家进行语音通信）
  enabled: true
  # 允许通信的服务器列表（留空表示允许与所有注册的服务器通信）
  # 如果指定了服务器列表，只能与列表中的服务器进行跨服务器通信
  allowed-servers: []
  # 示例：
  # allowed-servers:
  #   - "survival-server"
  #   - "creative-server"

# 跨世界通信配置
cross-world:
  # 是否启用跨世界通信（允许不同世界的玩家进行语音通信）
  enabled: true
  # 允许通信的世界列表（留空表示允许所有世界间通信）
  allowed-worlds: []
  # 示例：
  # allowed-worlds:
  #   - "world"
  #   - "world_nether"
  #   - "world_the_end"

# 权限配置
permissions:
  # 默认权限设置（当玩家没有特定权限时使用）
  defaults:
    # 基本语音权限
    listen: true          # 是否可以听到其他玩家
    speak: true           # 是否可以说话
    # 群组权限
    groups: true          # 是否可以使用群组功能
    create-group: true    # 是否可以创建群组
    join-group: true      # 是否可以加入群组
    # 跨区域权限
    cross-world: true     # 是否可以跨世界通信
    cross-server: true    # 是否可以跨服务器通信
    # 高级权限
    record: false         # 是否可以录音
    admin: false          # 是否有管理员权限
