package de.maxhenkel.voicechat.adapter.fabric.broadcast;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 语音广播管理器 - Fabric 版本
 */
public class VoiceBroadcastManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceBroadcastManager.class);
    
    private final FabricVoiceChatAdapter plugin;
    
    public VoiceBroadcastManager(FabricVoiceChatAdapter plugin) {
        this.plugin = plugin;
        
        LOGGER.info("Voice broadcast manager initialized");
    }
    
    public void shutdown() {
        LOGGER.info("Voice broadcast manager shut down");
    }
}
