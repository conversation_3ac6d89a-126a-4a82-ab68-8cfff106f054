# 跨服务器幽灵玩家问题测试指南

## 问题描述
当玩家从服务器A切换到服务器B时，房间中会出现两个相同的玩家，其中一个变成"幽灵人"。

## 修复内容

### 1. 服务器端修复
- **改进玩家登出处理**：在 `PlayerApiHandler.handlePlayerLogout()` 中添加了服务器验证和完整清理
- **优化玩家管理**：在 `PlayerManager.addPlayer()` 中正确处理服务器切换
- **增强状态同步**：在 `PlayerStateManager.syncCrossServerPlayerStates()` 中添加冲突检测
- **服务器关闭清理**：在 `ServerRegistrationManager.unregisterServer()` 中自动清理玩家

### 2. 客户端状态清理
- **定时清理机制**：添加了定时任务清理长时间断开连接的玩家状态
- **断开连接跟踪**：记录玩家断开连接的时间，超过阈值后自动清理
- **重复状态检测**：检测并忽略来自错误服务器的玩家状态

## 测试步骤

### 准备工作
1. 启动独立语音服务器
2. 启动服务器A（例如：survival）
3. 启动服务器B（例如：creative）
4. 确保两个服务器都正确连接到独立语音服务器

### 测试场景1：正常跨服务器切换
1. 玩家Alice在服务器A登录
2. 玩家Bob在服务器B登录
3. 两个玩家都加入同一个语音群组
4. 验证两个玩家都能正常看到对方
5. 玩家Alice从服务器A切换到服务器B
6. **预期结果**：房间中应该只有一个Alice和一个Bob，没有幽灵玩家

### 测试场景2：服务器关闭
1. 玩家Alice在服务器A，玩家Bob在服务器B
2. 两个玩家都在同一个语音群组中
3. 关闭服务器A
4. **预期结果**：玩家Alice应该从语音群组中消失，只剩下玩家Bob

### 测试场景3：网络中断恢复
1. 玩家Alice在服务器A，玩家Bob在服务器B
2. 模拟服务器A的网络中断（断开与独立语音服务器的连接）
3. 等待30秒后恢复连接
4. **预期结果**：玩家Alice应该重新出现在语音群组中，没有重复

## 验证方法

### 服务器日志检查
查看以下日志信息：

```
# 玩家切换服务器时
[INFO] Player Alice switching from server survival to server creative

# 清理过期玩家时
[DEBUG] Cleaned up long-disconnected player state: Alice

# 服务器关闭时
[INFO] Server 'survival' unregistered and all players cleaned up
[INFO] Cleaned up 3 players from server 'survival'
```

### 客户端验证
1. 打开语音聊天界面
2. 检查玩家列表中是否有重复的玩家名称
3. 验证语音通信是否正常工作
4. 确认没有"幽灵玩家"（显示但无法通信的玩家）

## 故障排除

### 如果仍然出现幽灵玩家
1. 检查服务器配置中的 `server-name` 是否唯一
2. 验证独立语音服务器的日志是否有错误
3. 确认网络连接稳定
4. 重启所有服务器以清理状态

### 常见问题
- **玩家状态不同步**：检查同步间隔配置，默认为5秒
- **清理任务不工作**：验证 `cleanupTask` 是否正常启动
- **服务器名称冲突**：确保每个服务器的名称都是唯一的

## 配置建议

### 同步配置优化
```yaml
sync:
  enabled: true
  interval: 3000  # 减少到3秒以提高响应速度
  sync-on-move: true
  min-move-distance: 1.0
```

### 服务器名称配置
```yaml
# 服务器A
server-name: "survival"

# 服务器B  
server-name: "creative"
```

## 监控建议
1. 定期检查独立语音服务器的玩家数量
2. 监控服务器日志中的清理信息
3. 观察客户端的语音聊天界面是否正常

通过以上修复和测试，跨服务器幽灵玩家问题应该得到解决。
