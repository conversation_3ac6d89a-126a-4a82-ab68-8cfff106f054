package de.maxhenkel.voicechat.adapter.fabric;

import de.maxhenkel.voicechat.adapter.fabric.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.fabric.network.SecretPacketSender;
import de.maxhenkel.voicechat.adapter.fabric.network.GroupMessageHandler;
import de.maxhenkel.voicechat.adapter.fabric.network.ConnectionManager;
import de.maxhenkel.voicechat.adapter.fabric.listeners.PlayerEventListener;
import de.maxhenkel.voicechat.adapter.fabric.managers.PlayerStateManager;
import de.maxhenkel.voicechat.adapter.fabric.commands.VoiceChatCommand;
import de.maxhenkel.voicechat.adapter.fabric.permission.PermissionSyncManager;
import de.maxhenkel.voicechat.adapter.fabric.group.AdvancedGroupManager;
import de.maxhenkel.voicechat.adapter.fabric.world.WorldConfigManager;
import de.maxhenkel.voicechat.adapter.fabric.broadcast.VoiceBroadcastManager;

import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents;
import net.fabricmc.loader.api.FabricLoader;
import net.minecraft.server.MinecraftServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Path;

/**
 * Fabric 语音聊天适配器主类 - 完全对应 VoiceChatAdapterPlugin
 */
public class FabricVoiceChatAdapter implements ModInitializer {
    
    public static final String MOD_ID = "fabric-voice-adapter";
    private static final Logger LOGGER = LoggerFactory.getLogger(FabricVoiceChatAdapter.class);
    
    private static FabricVoiceChatAdapter instance;
    private AdapterConfig config;
    private VoiceServerClient voiceServerClient;
    private ConnectionManager connectionManager;
    private SecretPacketSender secretPacketSender;
    private GroupMessageHandler groupMessageHandler;
    private PlayerStateManager playerStateManager;
    private PlayerEventListener playerEventListener;
    private PermissionSyncManager permissionSyncManager;
    private AdvancedGroupManager advancedGroupManager;
    private WorldConfigManager worldConfigManager;
    private VoiceBroadcastManager voiceBroadcastManager;
    
    private MinecraftServer server;
    
    @Override
    public void onInitialize() {
        instance = this;
        
        LOGGER.info("Initializing Fabric Voice Chat Adapter...");
        
        try {
            // 注册服务器生命周期事件
            registerServerLifecycleEvents();
            
            // 注册玩家连接事件
            registerPlayerConnectionEvents();
            
            LOGGER.info("Fabric Voice Chat Adapter initialized successfully!");
            
        } catch (Exception e) {
            LOGGER.error("Failed to initialize Fabric Voice Chat Adapter", e);
            throw new RuntimeException("Failed to initialize adapter", e);
        }
    }
    
    /**
     * 注册服务器生命周期事件
     */
    private void registerServerLifecycleEvents() {
        ServerLifecycleEvents.SERVER_STARTING.register(this::onServerStarting);
        ServerLifecycleEvents.SERVER_STARTED.register(this::onServerStarted);
        ServerLifecycleEvents.SERVER_STOPPING.register(this::onServerStopping);
    }
    
    /**
     * 注册玩家连接事件
     */
    private void registerPlayerConnectionEvents() {
        ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            if (playerEventListener != null) {
                playerEventListener.onPlayerJoin(handler.getPlayer());
            }
        });
        
        ServerPlayConnectionEvents.DISCONNECT.register((handler, server) -> {
            if (playerEventListener != null) {
                playerEventListener.onPlayerQuit(handler.getPlayer());
            }
        });
    }
    
    /**
     * 服务器启动中事件 - 对应 onEnable
     */
    private void onServerStarting(MinecraftServer server) {
        this.server = server;
        LOGGER.info("Enabling Fabric Voice Chat Adapter...");
        
        try {
            // 加载配置
            loadConfiguration();
            
            // 初始化语音服务器客户端
            initializeVoiceServerClient();

            // 初始化SecretPacket发送器
            initializeSecretPacketSender();

            // 初始化玩家状态管理器
            initializePlayerStateManager();

            // 初始化群组消息处理器
            initializeGroupMessageHandler();

            // 初始化连接管理器
            initializeConnectionManager();

            // 注册事件监听器
            registerEventListeners();

            // 初始化权限同步管理器
            initializePermissionSyncManager();

            // 初始化高级群组管理器
            initializeAdvancedGroupManager();

            // 初始化世界配置管理器
            initializeWorldConfigManager();

            // 初始化语音广播管理器
            initializeVoiceBroadcastManager();

            // 注册命令
            registerCommands();
            
            LOGGER.info("Fabric Voice Chat Adapter enabled successfully!");
            
        } catch (Exception e) {
            LOGGER.error("Failed to enable Fabric Voice Chat Adapter", e);
            throw new RuntimeException("Adapter initialization failed", e);
        }
    }
    
    /**
     * 服务器启动完成事件
     */
    private void onServerStarted(MinecraftServer server) {
        LOGGER.info("Server started, starting adapter services...");
        
        try {
            // 启动连接管理器
            if (connectionManager != null) {
                connectionManager.start();
            }
            
            // 注册服务器
            registerServerWithVoiceServer();
            
        } catch (Exception e) {
            LOGGER.error("Failed to start adapter services", e);
        }
    }
    
    /**
     * 服务器停止事件 - 对应 onDisable
     */
    private void onServerStopping(MinecraftServer server) {
        LOGGER.info("Disabling Fabric Voice Chat Adapter...");
        
        try {
            shutdown();
            LOGGER.info("Fabric Voice Chat Adapter disabled successfully!");
            
        } catch (Exception e) {
            LOGGER.error("Error during adapter shutdown", e);
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() throws Exception {
        Path configDir = FabricLoader.getInstance().getConfigDir().resolve(MOD_ID);
        if (!configDir.toFile().exists()) {
            configDir.toFile().mkdirs();
        }
        
        File configFile = configDir.resolve("config.yml").toFile();
        config = AdapterConfig.load(configFile);
        
        LOGGER.info("Configuration loaded from: {}", configFile.getAbsolutePath());
    }
    
    /**
     * 初始化语音服务器客户端
     */
    private void initializeVoiceServerClient() throws Exception {
        voiceServerClient = new VoiceServerClient(config);
        voiceServerClient.initialize();
        
        LOGGER.info("Voice server client initialized: {}", config.getVoiceServer().getApiEndpoint());
    }
    
    /**
     * 初始化SecretPacket发送器
     */
    private void initializeSecretPacketSender() {
        secretPacketSender = new SecretPacketSender(this, voiceServerClient);
        secretPacketSender.register();
        LOGGER.info("Secret packet sender initialized");
    }
    
    /**
     * 初始化玩家状态管理器
     */
    private void initializePlayerStateManager() {
        playerStateManager = new PlayerStateManager(this);
        playerStateManager.initialize();
        LOGGER.info("Player state manager initialized");
    }
    
    /**
     * 初始化群组消息处理器
     */
    private void initializeGroupMessageHandler() {
        groupMessageHandler = new GroupMessageHandler(this, voiceServerClient, playerStateManager);
        LOGGER.info("Group message handler initialized");
    }
    
    /**
     * 初始化连接管理器
     */
    private void initializeConnectionManager() {
        connectionManager = new ConnectionManager(this, voiceServerClient);
        LOGGER.info("Connection manager initialized");
    }
    
    /**
     * 注册事件监听器
     */
    private void registerEventListeners() {
        playerEventListener = new PlayerEventListener(this, voiceServerClient);
        LOGGER.info("Event listeners registered");
    }
    
    /**
     * 初始化权限同步管理器
     */
    private void initializePermissionSyncManager() {
        permissionSyncManager = new PermissionSyncManager(voiceServerClient, config);
        LOGGER.info("Permission sync manager initialized");
    }
    
    /**
     * 初始化高级群组管理器
     */
    private void initializeAdvancedGroupManager() {
        advancedGroupManager = new AdvancedGroupManager(voiceServerClient);
        LOGGER.info("Advanced group manager initialized");
    }
    
    /**
     * 初始化世界配置管理器
     */
    private void initializeWorldConfigManager() {
        worldConfigManager = new WorldConfigManager(voiceServerClient, config);
        LOGGER.info("World config manager initialized");
    }
    
    /**
     * 初始化语音广播管理器
     */
    private void initializeVoiceBroadcastManager() {
        voiceBroadcastManager = new VoiceBroadcastManager(this);
        LOGGER.info("Voice broadcast manager initialized");
    }
    
    /**
     * 注册命令
     */
    private void registerCommands() {
        VoiceChatCommand.register(this, voiceServerClient);
        LOGGER.info("Commands registered");
    }
    
    /**
     * 注册服务器到语音服务器
     */
    private void registerServerWithVoiceServer() {
        try {
            String serverName = config.getServerName();
            
            // 检查服务器名称是否可用
            if (!voiceServerClient.isServerNameAvailable(serverName)) {
                LOGGER.error("=".repeat(80));
                LOGGER.error("SERVER NAME CONFLICT DETECTED!");
                LOGGER.error("The server name '{}' is already registered with the voice server.", serverName);
                LOGGER.error("Please change the 'server-name' in your config.yml file to a unique name.");
                LOGGER.error("Current server name: {}", serverName);
                LOGGER.error("=".repeat(80));
                return;
            }
            
            // 尝试注册服务器
            String host = server.getServerIp().isEmpty() ? "localhost" : server.getServerIp();
            int port = server.getServerPort();
            
            if (voiceServerClient.registerServer(serverName, host, port)) {
                LOGGER.info("Server '{}' registered successfully with voice server", serverName);
            } else {
                LOGGER.warn("Failed to register server '{}' - name may have been taken by another server", serverName);
            }
            
        } catch (Exception e) {
            LOGGER.warn("Failed to register server during startup: {}", e.getMessage());
        }
    }
    
    /**
     * 关闭适配器 - 对应 onDisable 的内容
     */
    private void shutdown() {
        try {
            // 关闭连接管理器
            if (connectionManager != null) {
                connectionManager.stop();
            }
            
            // 关闭玩家状态管理器
            if (playerStateManager != null) {
                playerStateManager.shutdown();
            }
            
            // 关闭语音广播管理器
            if (voiceBroadcastManager != null) {
                voiceBroadcastManager.shutdown();
            }
            
            // 关闭SecretPacket发送器
            if (secretPacketSender != null) {
                secretPacketSender.unregister();
            }
            
            // 关闭权限同步管理器
            if (permissionSyncManager != null) {
                permissionSyncManager.shutdown();
            }
            
            // 取消注册服务器并关闭语音服务器客户端
            if (voiceServerClient != null) {
                try {
                    voiceServerClient.unregisterServer(config.getServerName());
                } catch (Exception e) {
                    LOGGER.warn("Failed to unregister server during shutdown: {}", e.getMessage());
                }
                voiceServerClient.shutdown();
            }
            
        } catch (Exception e) {
            LOGGER.error("Error during adapter shutdown", e);
        }
        
        instance = null;
    }
    
    // Getters - 完全对应 VoiceChatAdapterPlugin 的 getters
    public static FabricVoiceChatAdapter getInstance() {
        return instance;
    }
    
    public AdapterConfig getAdapterConfig() {
        return config;
    }
    
    public VoiceServerClient getVoiceServerClient() {
        return voiceServerClient;
    }
    
    public ConnectionManager getConnectionManager() {
        return connectionManager;
    }
    
    public GroupMessageHandler getGroupMessageHandler() {
        return groupMessageHandler;
    }
    
    public SecretPacketSender getSecretPacketSender() {
        return secretPacketSender;
    }
    
    public PlayerStateManager getPlayerStateManager() {
        return playerStateManager;
    }
    
    public PermissionSyncManager getPermissionSyncManager() {
        return permissionSyncManager;
    }
    
    public AdvancedGroupManager getAdvancedGroupManager() {
        return advancedGroupManager;
    }
    
    public WorldConfigManager getWorldConfigManager() {
        return worldConfigManager;
    }
    
    public VoiceBroadcastManager getVoiceBroadcastManager() {
        return voiceBroadcastManager;
    }
    
    public MinecraftServer getServer() {
        return server;
    }
}
