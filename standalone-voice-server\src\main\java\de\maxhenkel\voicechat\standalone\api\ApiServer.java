package de.maxhenkel.voicechat.standalone.api;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.server.VoiceServer;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import io.javalin.Javalin;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * API服务器，处理与Minecraft服务器的HTTP通信
 */
public class ApiServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiServer.class);
    
    private final ServerConfig config;
    private final VoiceServer voiceServer;
    private final PlayerApiHandler playerApiHandler;
    private final GroupApiHandler groupApiHandler;
    private final ConfigApiHandler configApiHandler;
    private final PermissionApiHandler permissionApiHandler;
    private final WorldApiHandler worldApiHandler;
    private final OpusApiHandler opusApiHandler;
    private final ServerRegistrationApiHandler serverRegistrationApiHandler;
    
    private Javalin app;
    private final long startTime; // 服务器启动时间

    public ApiServer(ServerConfig config, VoiceServer voiceServer) {
        this.config = config;
        this.voiceServer = voiceServer;
        this.playerApiHandler = new PlayerApiHandler(voiceServer);
        this.groupApiHandler = new GroupApiHandler(voiceServer);
        this.configApiHandler = new ConfigApiHandler(config);
        this.permissionApiHandler = new PermissionApiHandler(voiceServer);
        this.worldApiHandler = new WorldApiHandler(voiceServer);
        this.opusApiHandler = new OpusApiHandler(voiceServer);
        this.serverRegistrationApiHandler = new ServerRegistrationApiHandler(voiceServer.getServerRegistrationManager());
        this.startTime = System.currentTimeMillis(); // 记录启动时间
    }
    
    /**
     * 启动API服务器
     */
    public void start() {
        app = Javalin.create(javalinConfig -> {
            javalinConfig.showJavalinBanner = false;
            javalinConfig.jsonMapper(new JacksonJsonMapper());
        });
        
        // 添加认证中间件
        app.before(this::authenticate);
        
        // 注册路由
        registerRoutes();
        
        // 启动服务器
        app.start(config.getApi().getHost(), config.getApi().getPort());
        
        LOGGER.info("API server started on {}:{}", config.getApi().getHost(), config.getApi().getPort());
    }
    
    /**
     * 停止API服务器
     */
    public void stop() {
        if (app != null) {
            app.stop();
            LOGGER.info("API server stopped");
        }
    }
    
    /**
     * 认证中间件
     */
    private void authenticate(Context ctx) {
        // 跳过健康检查端点
        if (ctx.path().equals("/health")) {
            return;
        }
        
        String authHeader = ctx.header("Authorization");
        String expectedToken = "Bearer " + config.getApi().getAuthToken();
        
        if (authHeader == null || !authHeader.equals(expectedToken)) {
            ctx.status(HttpStatus.UNAUTHORIZED)
               .json(Map.of("error", "Unauthorized", "message", "Invalid or missing authorization token"));
        }
    }
    
    /**
     * 注册API路由
     */
    private void registerRoutes() {
        // 健康检查
        app.get("/health", ctx -> {
            ctx.json(Map.of(
                "status", "healthy",
                "timestamp", System.currentTimeMillis(),
                "startTime", startTime, // 添加启动时间
                "uptime", System.currentTimeMillis() - startTime, // 运行时间
                "version", "1.0.0"
            ));
        });
        
        // 服务器状态
        app.get("/api/status", ctx -> {
            ctx.json(Map.of(
                "voiceServer", Map.of(
                    "running", true,
                    "players", voiceServer.getPlayerManager().getStatistics(),
                    "groups", voiceServer.getGroupManager().getStatistics(),
                    "connections", voiceServer.getConnectionManager().getConnectionStatistics()
                )
            ));
        });
        
        // 玩家管理API
        app.post("/api/players/login", playerApiHandler::handlePlayerLogin);
        app.post("/api/players/logout", playerApiHandler::handlePlayerLogout);
        app.put("/api/players/{uuid}/position", playerApiHandler::handleUpdatePosition);
        app.put("/api/players/{uuid}/permissions", playerApiHandler::handleUpdatePermissions);
        app.get("/api/players/{uuid}/state", playerApiHandler::handleGetPlayerState);
        app.get("/api/players", playerApiHandler::handleGetAllPlayers);
        app.get("/api/players/connections", playerApiHandler::handleGetPlayerConnections);
        app.get("/api/players/online/all", playerApiHandler::handleGetAllOnlinePlayers);
        app.post("/api/players/{uuid}/secret", playerApiHandler::handleGenerateSecret);
        
        // 群组管理API
        app.post("/api/groups", groupApiHandler::handleCreateGroup);
        app.delete("/api/groups/{groupId}", groupApiHandler::handleDeleteGroup);
        app.post("/api/groups/{groupId}/members", groupApiHandler::handleJoinGroup);
        app.delete("/api/groups/{groupId}/members/{playerId}", groupApiHandler::handleLeaveGroup);
        app.delete("/api/players/{playerId}/group", groupApiHandler::handlePlayerLeaveGroup);
        app.get("/api/groups", groupApiHandler::handleGetAllGroups);
        app.get("/api/groups/{groupId}", groupApiHandler::handleGetGroup);

        // 高级群组管理API
        app.post("/api/groups/{groupId}/invite", groupApiHandler::handleInvitePlayer);
        app.post("/api/groups/{groupId}/kick", groupApiHandler::handleKickPlayer);
        app.post("/api/groups/{groupId}/ban", groupApiHandler::handleBanPlayer);
        app.post("/api/groups/{groupId}/moderator", groupApiHandler::handleSetModerator);
        app.post("/api/groups/{groupId}/transfer", groupApiHandler::handleTransferOwnership);
        
        // 配置管理API
        app.get("/api/config", configApiHandler::handleGetConfig);
        app.put("/api/config", configApiHandler::handleUpdateConfig);

        // 权限管理API
        app.get("/api/permissions/players/{uuid}", permissionApiHandler::handleGetPlayerPermissions);
        app.put("/api/permissions/players/{uuid}", permissionApiHandler::handleSetPlayerPermissions);
        app.get("/api/permissions/players/{uuid}/check/{permission}", permissionApiHandler::handleCheckPlayerPermission);
        app.post("/api/permissions/players/{uuid}/groups", permissionApiHandler::handleAddPlayerToGroup);
        app.delete("/api/permissions/players/{uuid}/groups/{groupName}", permissionApiHandler::handleRemovePlayerFromGroup);
        app.get("/api/permissions/groups", permissionApiHandler::handleGetAllGroups);
        app.post("/api/permissions/groups", permissionApiHandler::handleCreateGroup);
        app.delete("/api/permissions/groups/{groupName}", permissionApiHandler::handleDeleteGroup);
        app.get("/api/permissions/statistics", permissionApiHandler::handleGetStatistics);
        app.get("/api/permissions/available", permissionApiHandler::handleGetAvailablePermissions);

        // 世界和服务器管理API
        app.get("/api/worlds", worldApiHandler::handleGetAllWorldConfigs);
        app.get("/api/worlds/{worldName}", worldApiHandler::handleGetWorldConfig);
        app.put("/api/worlds/{worldName}", worldApiHandler::handleUpdateWorldConfig);
        app.get("/api/servers/{serverName}", worldApiHandler::handleGetServerConfig);
        app.put("/api/servers/{serverName}", worldApiHandler::handleUpdateServerConfig);
        app.post("/api/worlds/cross-world-rules", worldApiHandler::handleAddCrossWorldRule);
        app.delete("/api/worlds/cross-world-rules", worldApiHandler::handleRemoveCrossWorldRule);
        app.post("/api/servers/cross-server-rules", worldApiHandler::handleAddCrossServerRule);
        app.delete("/api/servers/cross-server-rules", worldApiHandler::handleRemoveCrossServerRule);

        // Opus编解码器管理API
        app.get("/api/opus/config", opusApiHandler::handleGetOpusConfig);
        app.put("/api/opus/config", opusApiHandler::handleUpdateOpusConfig);
        app.get("/api/opus/codecs", opusApiHandler::handleGetAvailableCodecs);
        app.get("/api/opus/statistics", opusApiHandler::handleGetOpusStatistics);
        app.get("/api/opus/validate-bitrate/{bitrate}", opusApiHandler::handleValidateBitrate);
        app.post("/api/opus/reset", opusApiHandler::handleResetOpusConfig);
        app.get("/api/opus/recommendations", opusApiHandler::handleGetRecommendations);

        // 广播管理API
        app.post("/api/broadcast/start", this::handleStartBroadcast);
        app.post("/api/broadcast/stop", this::handleStopBroadcast);
        app.get("/api/broadcast/status", this::handleGetBroadcastStatus);

        // 世界配置同步API
        app.post("/api/worlds/sync-configs", this::handleSyncWorldConfigs);
        app.post("/api/worlds/sync-config", this::handleSyncWorldConfig);

        // 玩家游戏模式API
        app.put("/api/players/{uuid}/gamemode", this::handleUpdatePlayerGameMode);

        // 观察者权限API
        app.post("/api/players/{uuid}/permissions/spectator-voice/grant", this::handleGrantSpectatorVoice);
        app.post("/api/players/{uuid}/permissions/spectator-voice/revoke", this::handleRevokeSpectatorVoice);

        // 服务器注册管理API
        app.post("/api/servers/register", serverRegistrationApiHandler::handleRegisterServer);
        app.post("/api/servers/{serverName}/keepalive", serverRegistrationApiHandler::handleKeepalive);
        app.delete("/api/servers/register/{serverName}", serverRegistrationApiHandler::handleUnregisterServer);
        app.get("/api/servers/{serverName}/available", serverRegistrationApiHandler::handleCheckServerNameAvailable);
        app.get("/api/servers/register/{serverName}", serverRegistrationApiHandler::handleGetServerInfo);
        app.get("/api/servers/register", serverRegistrationApiHandler::handleGetAllServers);
        app.get("/api/servers/register/statistics", serverRegistrationApiHandler::handleGetStatistics);

        // 错误处理
        app.exception(Exception.class, (e, ctx) -> {
            LOGGER.error("API error", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of(
                   "error", "Internal Server Error",
                   "message", e.getMessage()
               ));
        });
        
        // 404处理
        app.error(HttpStatus.NOT_FOUND, ctx -> {
            ctx.json(Map.of(
                "error", "Not Found",
                "message", "The requested endpoint was not found"
            ));
        });
    }

    /**
     * 开始广播
     * POST /api/broadcast/start
     */
    private void handleStartBroadcast(Context ctx) {
        try {
            Map<String, Object> requestBody = ctx.bodyAsClass(Map.class);
            String playerUuidStr = (String) requestBody.get("playerUuid");

            if (playerUuidStr == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "playerUuid is required"));
                return;
            }

            UUID playerUuid = UUID.fromString(playerUuidStr);

            // 检查玩家是否存在
            if (voiceServer.getPlayerManager().getPlayer(playerUuid) == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }

            // 开始广播
            voiceServer.startBroadcast(playerUuid);

            ctx.json(Map.of(
                "success", true,
                "message", "Broadcast started",
                "playerUuid", playerUuidStr
            ));

        } catch (Exception e) {
            LOGGER.error("Error starting broadcast", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 停止广播
     * POST /api/broadcast/stop
     */
    private void handleStopBroadcast(Context ctx) {
        try {
            Map<String, Object> requestBody = ctx.bodyAsClass(Map.class);
            String playerUuidStr = (String) requestBody.get("playerUuid");

            if (playerUuidStr == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "playerUuid is required"));
                return;
            }

            UUID playerUuid = UUID.fromString(playerUuidStr);

            // 停止广播
            voiceServer.stopBroadcast(playerUuid);

            ctx.json(Map.of(
                "success", true,
                "message", "Broadcast stopped",
                "playerUuid", playerUuidStr
            ));

        } catch (Exception e) {
            LOGGER.error("Error stopping broadcast", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 获取广播状态
     * GET /api/broadcast/status
     */
    private void handleGetBroadcastStatus(Context ctx) {
        try {
            // 获取所有正在广播的玩家
            List<Map<String, Object>> activeBroadcasters = new ArrayList<>();

            for (PlayerData player : voiceServer.getPlayerManager().getAllPlayers()) {
                if (voiceServer.isBroadcasting(player.getUuid())) {
                    activeBroadcasters.add(Map.of(
                        "uuid", player.getUuid().toString(),
                        "name", player.getName(),
                        "serverName", player.getServerName()
                    ));
                }
            }

            ctx.json(Map.of(
                "activeBroadcasters", activeBroadcasters,
                "count", activeBroadcasters.size()
            ));

        } catch (Exception e) {
            LOGGER.error("Error getting broadcast status", e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR)
               .json(Map.of("error", "Internal server error"));
        }
    }

    /**
     * 同步多个世界配置
     * POST /api/worlds/sync-configs
     */
    private void handleSyncWorldConfigs(Context ctx) {
        try {
            Map<String, Object> requestBody = ctx.bodyAsClass(Map.class);
            String serverName = (String) requestBody.get("serverName");
            Map<String, Map<String, Object>> worldConfigs = (Map<String, Map<String, Object>>) requestBody.get("worldConfigs");

            if (serverName == null || worldConfigs == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "serverName and worldConfigs are required"));
                return;
            }

            // 更新世界配置
            int updatedCount = 0;
            for (Map.Entry<String, Map<String, Object>> entry : worldConfigs.entrySet()) {
                String worldName = entry.getKey();
                Map<String, Object> worldConfig = entry.getValue();

                try {
                    voiceServer.getWorldManager().updateWorldConfigFromAdapter(worldName, worldConfig);
                    updatedCount++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to update world config for {}: {}", worldName, e.getMessage());
                }
            }

            ctx.json(Map.of(
                "success", true,
                "message", "World configs synced",
                "serverName", serverName,
                "updatedCount", updatedCount,
                "totalCount", worldConfigs.size()
            ));

        } catch (Exception e) {
            LOGGER.error("Error syncing world configs", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 同步单个世界配置
     * POST /api/worlds/sync-config
     */
    private void handleSyncWorldConfig(Context ctx) {
        try {
            Map<String, Object> requestBody = ctx.bodyAsClass(Map.class);
            String serverName = (String) requestBody.get("serverName");
            String worldName = (String) requestBody.get("worldName");
            Map<String, Object> worldConfig = (Map<String, Object>) requestBody.get("worldConfig");

            if (serverName == null || worldName == null || worldConfig == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "serverName, worldName and worldConfig are required"));
                return;
            }

            // 更新世界配置
            voiceServer.getWorldManager().updateWorldConfigFromAdapter(worldName, worldConfig);

            ctx.json(Map.of(
                "success", true,
                "message", "World config synced",
                "serverName", serverName,
                "worldName", worldName
            ));

        } catch (Exception e) {
            LOGGER.error("Error syncing world config", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 更新玩家游戏模式
     * PUT /api/players/{uuid}/gamemode
     */
    private void handleUpdatePlayerGameMode(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);

            Map<String, Object> requestBody = ctx.bodyAsClass(Map.class);
            Number gameModeNum = (Number) requestBody.get("gameMode");

            if (gameModeNum == null) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "gameMode is required"));
                return;
            }

            int gameMode = gameModeNum.intValue();

            // 验证游戏模式范围
            if (gameMode < 0 || gameMode > 3) {
                ctx.status(HttpStatus.BAD_REQUEST)
                   .json(Map.of("error", "gameMode must be 0-3 (0=Survival, 1=Creative, 2=Adventure, 3=Spectator)"));
                return;
            }

            // 更新玩家游戏模式
            PlayerData player = voiceServer.getPlayerManager().getPlayer(playerUuid);
            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }

            player.setGameMode(gameMode);

            ctx.json(Map.of(
                "success", true,
                "message", "Game mode updated",
                "playerUuid", playerUuid.toString(),
                "gameMode", gameMode
            ));

            LOGGER.debug("Updated game mode for player {}: {}", player.getName(), gameMode);

        } catch (Exception e) {
            LOGGER.error("Error updating player game mode", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 授予观察者语音权限
     * POST /api/players/{uuid}/permissions/spectator-voice/grant
     */
    private void handleGrantSpectatorVoice(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);

            PlayerData player = voiceServer.getPlayerManager().getPlayer(playerUuid);
            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }

            // 授予观察者交互权限
            voiceServer.getPermissionManager().setPlayerPermission(
                playerUuid,
                de.maxhenkel.voicechat.standalone.model.Permission.SPECTATOR_INTERACTION,
                true
            );

            ctx.json(Map.of(
                "success", true,
                "message", "Spectator voice permission granted",
                "playerUuid", playerUuid.toString(),
                "playerName", player.getName()
            ));

            LOGGER.info("Granted spectator voice permission to player {}", player.getName());

        } catch (Exception e) {
            LOGGER.error("Error granting spectator voice permission", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }

    /**
     * 撤销观察者语音权限
     * POST /api/players/{uuid}/permissions/spectator-voice/revoke
     */
    private void handleRevokeSpectatorVoice(Context ctx) {
        try {
            String uuidStr = ctx.pathParam("uuid");
            UUID playerUuid = UUID.fromString(uuidStr);

            PlayerData player = voiceServer.getPlayerManager().getPlayer(playerUuid);
            if (player == null) {
                ctx.status(HttpStatus.NOT_FOUND)
                   .json(Map.of("error", "Player not found"));
                return;
            }

            // 撤销观察者交互权限
            voiceServer.getPermissionManager().setPlayerPermission(
                playerUuid,
                de.maxhenkel.voicechat.standalone.model.Permission.SPECTATOR_INTERACTION,
                false
            );

            ctx.json(Map.of(
                "success", true,
                "message", "Spectator voice permission revoked",
                "playerUuid", playerUuid.toString(),
                "playerName", player.getName()
            ));

            LOGGER.info("Revoked spectator voice permission from player {}", player.getName());

        } catch (Exception e) {
            LOGGER.error("Error revoking spectator voice permission", e);
            ctx.status(HttpStatus.BAD_REQUEST)
               .json(Map.of("error", "Invalid request: " + e.getMessage()));
        }
    }
}
