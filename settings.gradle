pluginManagement {
    repositories {
        gradlePluginPortal()
        maven { url = 'https://maven.fabricmc.net/' }
        maven { url = 'https://repo.spongepowered.org/repository/maven-public/' }
        maven { url = 'https://maven.neoforged.net/releases' }
        maven { url = 'https://maven.maxhenkel.de/repository/public' }
        maven { url = 'https://maven.neoforged.net/releases' }
        maven { url = 'https://maven.minecraftforge.net' }
        maven { url = 'https://repo.papermc.io/repository/maven-public/' }
        maven { url = 'https://repo.u-team.info' }
    }
}

rootProject.name = 'voicechat'
include('api', 'common', 'common-client', 'common-proxy', 'fabric', 'bukkit', 'paper', 'velocity', 'bungeecord')

// 新增：独立语音服务器模块和适配器
include('standalone-voice-server', 'minecraft-adapter', 'fabric-adapter')