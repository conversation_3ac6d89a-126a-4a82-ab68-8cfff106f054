package de.maxhenkel.voicechat.adapter.fabric.network;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 连接管理器 - Fabric 版本
 */
public class ConnectionManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectionManager.class);
    
    private final FabricVoiceChatAdapter plugin;
    private final VoiceServerClient voiceServerClient;
    
    public ConnectionManager(FabricVoiceChatAdapter plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
        
        LOGGER.info("Connection manager initialized");
    }
    
    public void start() {
        LOGGER.info("Connection manager started");
    }
    
    public void stop() {
        LOGGER.info("Connection manager stopped");
    }
}
