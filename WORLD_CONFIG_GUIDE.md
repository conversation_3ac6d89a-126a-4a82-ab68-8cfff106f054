# 世界配置指南

## 概述

Voice Chat Adapter 现在支持为不同的世界配置不同的语音聊天设置。您可以为每个世界单独设置最大语音距离、是否启用语音、群组功能等。

## 配置文件位置

- **Bukkit/Spigot/Paper**: `plugins/VoiceChatAdapter/config.yml`
- **Fabric**: `config/voicechat-adapter/config.yml`

## 配置结构

```yaml
worlds:
  # 默认配置（适用于所有未特别配置的世界）
  default:
    voice-enabled: true
    max-voice-distance: 48.0
    groups-enabled: true
    cross-world-allowed: false
  
  # 特定世界配置
  specific:
    world_name:
      voice-enabled: true
      max-voice-distance: 48.0
      groups-enabled: true
      cross-world-allowed: false
```

## 配置选项说明

### `voice-enabled`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否在该世界启用语音聊天
- **示例**: 在 PvP 世界中禁用语音聊天

### `max-voice-distance`
- **类型**: double
- **默认值**: 48.0
- **说明**: 最大语音收听距离（以方块为单位）
- **示例**: 地狱中设置较短距离 (24.0)，末地中设置较远距离 (64.0)

### `groups-enabled`
- **类型**: boolean
- **默认值**: true
- **说明**: 是否允许在该世界使用群组语音功能

### `cross-world-allowed`
- **类型**: boolean
- **默认值**: false
- **说明**: 是否允许该世界的玩家与其他世界的玩家进行语音通信

## 配置示例

### 基本配置
```yaml
worlds:
  default:
    voice-enabled: true
    max-voice-distance: 48.0
    groups-enabled: true
    cross-world-allowed: false
  
  specific:
    # 主世界 - 标准设置
    world:
      voice-enabled: true
      max-voice-distance: 48.0
      groups-enabled: true
      cross-world-allowed: false
    
    # 地狱 - 较短语音距离
    world_nether:
      voice-enabled: true
      max-voice-distance: 24.0
      groups-enabled: true
      cross-world-allowed: false
    
    # 末地 - 较远语音距离
    world_the_end:
      voice-enabled: true
      max-voice-distance: 64.0
      groups-enabled: true
      cross-world-allowed: false
```

### 高级配置
```yaml
worlds:
  default:
    voice-enabled: true
    max-voice-distance: 48.0
    groups-enabled: true
    cross-world-allowed: false
  
  specific:
    # PvP 世界 - 完全禁用语音
    world_pvp:
      voice-enabled: false
      max-voice-distance: 0.0
      groups-enabled: false
      cross-world-allowed: false
    
    # 创造世界 - 允许跨世界语音
    world_creative:
      voice-enabled: true
      max-voice-distance: 96.0
      groups-enabled: true
      cross-world-allowed: true
    
    # 小游戏世界 - 只允许群组语音
    world_minigames:
      voice-enabled: true
      max-voice-distance: 16.0
      groups-enabled: true
      cross-world-allowed: false
    
    # 安静区域 - 禁用群组功能
    world_library:
      voice-enabled: true
      max-voice-distance: 8.0
      groups-enabled: false
      cross-world-allowed: false
```

## 应用配置

1. 编辑配置文件
2. 重启服务器或使用重载命令（如果支持）
3. 配置将自动同步到独立语音服务器

## 注意事项

- 如果某个世界没有特定配置，将使用 `default` 配置
- 配置更改需要重启服务器才能生效
- `max-voice-distance` 设置为 0 等同于禁用语音聊天
- `cross-world-allowed` 需要配合全局跨世界设置使用

## 故障排除

### 配置不生效
1. 检查 YAML 语法是否正确
2. 确认世界名称拼写正确
3. 查看服务器日志中的配置加载信息

### 语音距离异常
1. 确认 `max-voice-distance` 值为正数
2. 检查是否与其他插件冲突
3. 验证独立语音服务器是否正常运行

## 相关功能

此世界配置功能与以下功能配合使用：
- 跨服务器语音通信
- 群组语音聊天
- 权限管理系统
- 玩家状态同步
