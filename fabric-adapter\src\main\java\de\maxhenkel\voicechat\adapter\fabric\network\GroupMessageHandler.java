package de.maxhenkel.voicechat.adapter.fabric.network;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import de.maxhenkel.voicechat.adapter.fabric.managers.PlayerStateManager;
import net.minecraft.server.network.ServerPlayerEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 群组消息处理器 - Fabric 版本
 * 完全对应 minecraft-adapter 的 GroupMessageHandler
 */
public class GroupMessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupMessageHandler.class);

    private final FabricVoiceChatAdapter plugin;
    private final VoiceServerClient voiceServerClient;
    private final PlayerStateManager playerStateManager;

    public GroupMessageHandler(FabricVoiceChatAdapter plugin,
                             VoiceServerClient voiceServerClient,
                             PlayerStateManager playerStateManager) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
        this.playerStateManager = playerStateManager;

        LOGGER.info("Group message handler initialized");
    }

    /**
     * 为玩家同步群组信息
     */
    public void syncGroupsForPlayer(ServerPlayerEntity player) {
        try {
            // 获取所有群组
            List<Map<String, Object>> groups = voiceServerClient.getAllGroups();
            if (groups != null) {
                LOGGER.debug("Synced {} groups for player {}", groups.size(), player.getName().getString());
                // TODO: 实现群组信息发送到客户端
            }
        } catch (Exception e) {
            LOGGER.error("Failed to sync groups for player {}", player.getName().getString(), e);
        }
    }

    /**
     * 处理群组创建请求
     */
    public UUID handleCreateGroup(ServerPlayerEntity player, String groupName, String password, int groupType) {
        try {
            UUID groupUuid = voiceServerClient.createGroup(player.getUuid(), groupName, password, groupType);
            if (groupUuid != null) {
                LOGGER.info("Player {} created group {} ({})", player.getName().getString(), groupName, groupUuid);
            }
            return groupUuid;
        } catch (Exception e) {
            LOGGER.error("Failed to create group {} for player {}", groupName, player.getName().getString(), e);
            return null;
        }
    }

    /**
     * 处理加入群组请求
     */
    public boolean handleJoinGroup(ServerPlayerEntity player, UUID groupUuid, String password) {
        try {
            boolean success = voiceServerClient.joinGroup(player.getUuid(), groupUuid, password);
            if (success) {
                LOGGER.info("Player {} joined group {}", player.getName().getString(), groupUuid);
            }
            return success;
        } catch (Exception e) {
            LOGGER.error("Failed to join group {} for player {}", groupUuid, player.getName().getString(), e);
            return false;
        }
    }

    /**
     * 处理离开群组请求
     */
    public boolean handleLeaveGroup(ServerPlayerEntity player) {
        try {
            boolean success = voiceServerClient.leaveGroup(player.getUuid());
            if (success) {
                LOGGER.info("Player {} left their group", player.getName().getString());
            }
            return success;
        } catch (Exception e) {
            LOGGER.error("Failed to leave group for player {}", player.getName().getString(), e);
            return false;
        }
    }

    /**
     * 获取群组信息
     */
    public Map<String, Object> getGroupInfo(UUID groupUuid) {
        return voiceServerClient.getGroupInfo(groupUuid);
    }
}
