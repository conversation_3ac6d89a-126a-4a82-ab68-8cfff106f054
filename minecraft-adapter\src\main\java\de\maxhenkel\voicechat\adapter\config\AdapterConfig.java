package de.maxhenkel.voicechat.adapter.config;

import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;

/**
 * 适配器配置类
 */
public class AdapterConfig {
    
    private final VoiceServerConfig voiceServer;
    private final SyncConfig sync;
    private final String serverName;
    private final CrossServerConfig crossServer;
    private final CrossWorldConfig crossWorld;
    private final PermissionConfig permissions;
    private final WorldsConfig worlds;
    
    public AdapterConfig(VoiceServerConfig voiceServer, SyncConfig sync, String serverName,
                        CrossServerConfig crossServer, CrossWorldConfig crossWorld, PermissionConfig permissions,
                        WorldsConfig worlds) {
        this.voiceServer = voiceServer;
        this.sync = sync;
        this.serverName = serverName;
        this.crossServer = crossServer;
        this.crossWorld = crossWorld;
        this.permissions = permissions;
        this.worlds = worlds;
    }
    
    /**
     * 语音服务器配置
     */
    public static class VoiceServerConfig {
        private final String host;
        private final int port;
        private final String apiEndpoint;
        private final String authToken;
        
        public VoiceServerConfig(String host, int port, String apiEndpoint, String authToken) {
            this.host = host;
            this.port = port;
            this.apiEndpoint = apiEndpoint;
            this.authToken = authToken;
        }
        
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getApiEndpoint() { return apiEndpoint; }
        public String getAuthToken() { return authToken; }
    }
    
    /**
     * 同步配置
     */
    public static class SyncConfig {
        private final int positionSyncInterval;
        private final int permissionSyncInterval;
        private final boolean syncOnMove;
        private final double minMoveDistance;
        
        public SyncConfig(int positionSyncInterval, int permissionSyncInterval, 
                         boolean syncOnMove, double minMoveDistance) {
            this.positionSyncInterval = positionSyncInterval;
            this.permissionSyncInterval = permissionSyncInterval;
            this.syncOnMove = syncOnMove;
            this.minMoveDistance = minMoveDistance;
        }
        
        public int getPositionSyncInterval() { return positionSyncInterval; }
        public int getPermissionSyncInterval() { return permissionSyncInterval; }
        public boolean isSyncOnMove() { return syncOnMove; }
        public double getMinMoveDistance() { return minMoveDistance; }
    }
    
    /**
     * 从文件加载配置
     */
    public static AdapterConfig load(File file) throws Exception {
        YamlConfiguration yaml = YamlConfiguration.loadConfiguration(file);
        
        // 语音服务器配置
        String host = yaml.getString("voice-server.host", "localhost");
        int port = yaml.getInt("voice-server.port", 24454);
        String apiEndpoint = yaml.getString("voice-server.api-endpoint", "http://localhost:8080");
        String authToken = yaml.getString("voice-server.auth-token", "change-this-secret-token");
        
        VoiceServerConfig voiceServerConfig = new VoiceServerConfig(host, port, apiEndpoint, authToken);
        
        // 同步配置
        int positionSyncInterval = yaml.getInt("sync.position-interval", 1000);
        int permissionSyncInterval = yaml.getInt("sync.permission-interval", 5000);
        boolean syncOnMove = yaml.getBoolean("sync.sync-on-move", true);
        double minMoveDistance = yaml.getDouble("sync.min-move-distance", 1.0);
        
        SyncConfig syncConfig = new SyncConfig(positionSyncInterval, permissionSyncInterval, 
                                              syncOnMove, minMoveDistance);
        
        // 服务器名称
        String serverName = yaml.getString("server-name", "default");

        // 跨服务器配置
        boolean crossServerEnabled = yaml.getBoolean("cross-server.enabled", true);
        java.util.List<String> allowedServers = yaml.getStringList("cross-server.allowed-servers");
        CrossServerConfig crossServerConfig = new CrossServerConfig(crossServerEnabled, allowedServers);

        // 跨世界配置
        boolean crossWorldEnabled = yaml.getBoolean("cross-world.enabled", true);
        java.util.List<String> allowedWorlds = yaml.getStringList("cross-world.allowed-worlds");
        CrossWorldConfig crossWorldConfig = new CrossWorldConfig(crossWorldEnabled, allowedWorlds);

        // 权限配置
        PermissionConfig permissionConfig = loadPermissionConfig(yaml);

        // 世界配置
        WorldsConfig worldsConfig = loadWorldsConfig(yaml);

        return new AdapterConfig(voiceServerConfig, syncConfig, serverName,
                               crossServerConfig, crossWorldConfig, permissionConfig, worldsConfig);
    }
    
    /**
     * 加载权限配置
     */
    private static PermissionConfig loadPermissionConfig(YamlConfiguration yaml) {
        boolean listen = yaml.getBoolean("permissions.defaults.listen", true);
        boolean speak = yaml.getBoolean("permissions.defaults.speak", true);
        boolean groups = yaml.getBoolean("permissions.defaults.groups", true);
        boolean createGroup = yaml.getBoolean("permissions.defaults.create-group", true);
        boolean joinGroup = yaml.getBoolean("permissions.defaults.join-group", true);
        boolean crossWorld = yaml.getBoolean("permissions.defaults.cross-world", true);
        boolean crossServer = yaml.getBoolean("permissions.defaults.cross-server", true);
        boolean record = yaml.getBoolean("permissions.defaults.record", false);
        boolean admin = yaml.getBoolean("permissions.defaults.admin", false);

        return new PermissionConfig(listen, speak, groups, createGroup, joinGroup,
                                  crossWorld, crossServer, record, admin);
    }

    /**
     * 加载世界配置
     */
    private static WorldsConfig loadWorldsConfig(org.bukkit.configuration.file.YamlConfiguration yaml) {
        // 默认世界配置
        double defaultMaxDistance = yaml.getDouble("worlds.default.max-voice-distance", 48.0);
        boolean defaultVoiceEnabled = yaml.getBoolean("worlds.default.voice-enabled", true);
        boolean defaultGroupsEnabled = yaml.getBoolean("worlds.default.groups-enabled", true);
        boolean defaultCrossWorldAllowed = yaml.getBoolean("worlds.default.cross-world-allowed", false);

        WorldConfig defaultConfig = new WorldConfig(defaultVoiceEnabled, defaultMaxDistance,
                                                   defaultGroupsEnabled, defaultCrossWorldAllowed);

        // 特定世界配置
        java.util.Map<String, WorldConfig> worldConfigs = new java.util.HashMap<>();

        if (yaml.contains("worlds.specific")) {
            org.bukkit.configuration.ConfigurationSection specificSection = yaml.getConfigurationSection("worlds.specific");
            if (specificSection != null) {
                for (String worldName : specificSection.getKeys(false)) {
                    double maxDistance = specificSection.getDouble(worldName + ".max-voice-distance", defaultMaxDistance);
                    boolean voiceEnabled = specificSection.getBoolean(worldName + ".voice-enabled", defaultVoiceEnabled);
                    boolean groupsEnabled = specificSection.getBoolean(worldName + ".groups-enabled", defaultGroupsEnabled);
                    boolean crossWorldAllowed = specificSection.getBoolean(worldName + ".cross-world-allowed", defaultCrossWorldAllowed);

                    WorldConfig worldConfig = new WorldConfig(voiceEnabled, maxDistance, groupsEnabled, crossWorldAllowed);
                    worldConfigs.put(worldName, worldConfig);
                }
            }
        }

        return new WorldsConfig(defaultConfig, worldConfigs);
    }

    // Getters
    public VoiceServerConfig getVoiceServer() { return voiceServer; }
    public SyncConfig getSync() { return sync; }
    public String getServerName() { return serverName; }
    public CrossServerConfig getCrossServer() { return crossServer; }
    public CrossWorldConfig getCrossWorld() { return crossWorld; }
    public PermissionConfig getPermissions() { return permissions; }
    public WorldsConfig getWorlds() { return worlds; }

    /**
     * 跨服务器配置
     */
    public static class CrossServerConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedServers;

        public CrossServerConfig(boolean enabled, java.util.List<String> allowedServers) {
            this.enabled = enabled;
            this.allowedServers = allowedServers != null ? allowedServers : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedServers() { return allowedServers; }
        public boolean isServerAllowed(String serverName) {
            return allowedServers.isEmpty() || allowedServers.contains(serverName);
        }
    }

    /**
     * 跨世界配置
     */
    public static class CrossWorldConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedWorlds;

        public CrossWorldConfig(boolean enabled, java.util.List<String> allowedWorlds) {
            this.enabled = enabled;
            this.allowedWorlds = allowedWorlds != null ? allowedWorlds : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedWorlds() { return allowedWorlds; }
        public boolean isWorldAllowed(String worldName) {
            return allowedWorlds.isEmpty() || allowedWorlds.contains(worldName);
        }
    }

    /**
     * 权限配置
     */
    public static class PermissionConfig {
        private final boolean listen;
        private final boolean speak;
        private final boolean groups;
        private final boolean createGroup;
        private final boolean joinGroup;
        private final boolean crossWorld;
        private final boolean crossServer;
        private final boolean record;
        private final boolean admin;

        public PermissionConfig(boolean listen, boolean speak, boolean groups, boolean createGroup,
                              boolean joinGroup, boolean crossWorld, boolean crossServer,
                              boolean record, boolean admin) {
            this.listen = listen;
            this.speak = speak;
            this.groups = groups;
            this.createGroup = createGroup;
            this.joinGroup = joinGroup;
            this.crossWorld = crossWorld;
            this.crossServer = crossServer;
            this.record = record;
            this.admin = admin;
        }

        public boolean isListen() { return listen; }
        public boolean isSpeak() { return speak; }
        public boolean isGroups() { return groups; }
        public boolean isCreateGroup() { return createGroup; }
        public boolean isJoinGroup() { return joinGroup; }
        public boolean isCrossWorld() { return crossWorld; }
        public boolean isCrossServer() { return crossServer; }
        public boolean isRecord() { return record; }
        public boolean isAdmin() { return admin; }
    }

    /**
     * 世界配置
     */
    public static class WorldsConfig {
        private final WorldConfig defaultConfig;
        private final java.util.Map<String, WorldConfig> worldConfigs;

        public WorldsConfig(WorldConfig defaultConfig, java.util.Map<String, WorldConfig> worldConfigs) {
            this.defaultConfig = defaultConfig;
            this.worldConfigs = worldConfigs != null ? worldConfigs : new java.util.HashMap<>();
        }

        public WorldConfig getDefaultConfig() { return defaultConfig; }
        public java.util.Map<String, WorldConfig> getWorldConfigs() { return worldConfigs; }

        /**
         * 获取指定世界的配置，如果不存在则返回默认配置
         */
        public WorldConfig getWorldConfig(String worldName) {
            return worldConfigs.getOrDefault(worldName, defaultConfig);
        }
    }

    /**
     * 单个世界的配置
     */
    public static class WorldConfig {
        private final boolean voiceEnabled;
        private final double maxVoiceDistance;
        private final boolean groupsEnabled;
        private final boolean crossWorldAllowed;

        public WorldConfig(boolean voiceEnabled, double maxVoiceDistance,
                          boolean groupsEnabled, boolean crossWorldAllowed) {
            this.voiceEnabled = voiceEnabled;
            this.maxVoiceDistance = maxVoiceDistance;
            this.groupsEnabled = groupsEnabled;
            this.crossWorldAllowed = crossWorldAllowed;
        }

        public boolean isVoiceEnabled() { return voiceEnabled; }
        public double getMaxVoiceDistance() { return maxVoiceDistance; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public boolean isCrossWorldAllowed() { return crossWorldAllowed; }
    }
}
