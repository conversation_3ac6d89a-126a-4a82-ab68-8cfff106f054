package de.maxhenkel.voicechat.adapter.fabric.network;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import de.maxhenkel.voicechat.adapter.fabric.config.AdapterConfig;
import net.minecraft.server.network.ServerPlayerEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.UUID;

/**
 * 负责发送SecretPacket到客户端 - Fabric 版本
 * 完全对应 minecraft-adapter 的 SecretPacketSender
 */
public class SecretPacketSender {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SecretPacketSender.class);
    
    // 语音聊天插件通道 - 与原版完全一致
    public static final String VOICE_CHAT_CHANNEL = "voicechat:secret";
    public static final String REQUEST_CHANNEL = "voicechat:request_secret";
    
    private final FabricVoiceChatAdapter plugin;
    private final VoiceServerClient voiceServerClient;
    
    public SecretPacketSender(FabricVoiceChatAdapter plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
    }
    
    /**
     * 注册网络通道
     */
    public void register() {
        // TODO: 实现 Fabric 网络通道注册
        // 暂时跳过复杂的网络处理，专注于核心功能
        LOGGER.info("Registered voice chat plugin message channels (simplified)");
    }
    
    /**
     * 注销网络通道
     */
    public void unregister() {
        // TODO: 实现网络通道注销
        LOGGER.info("Unregistered voice chat plugin message channels (simplified)");
    }
    
    /**
     * 发送SecretPacket到客户端
     * 使用与原版完全兼容的格式
     */
    public void sendSecretPacket(ServerPlayerEntity player) throws Exception {
        AdapterConfig config = plugin.getAdapterConfig();

        // 确保玩家已在语音服务器中注册
        try {
            voiceServerClient.ensurePlayerRegistered(player);
        } catch (Exception e) {
            LOGGER.warn("Failed to register player {}, attempting to continue: {}", player.getName().getString(), e.getMessage());
        }

        // 从语音服务器获取认证密钥
        String secret = voiceServerClient.generatePlayerSecret(player.getUuid());

        // 使用Minecraft的FriendlyByteBuf格式（与原版一致）
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(baos);

        try {
            // 按照原版SecretPacket的确切格式写入数据
            writeUUID(dos, UUID.fromString(secret));  // secret (16 bytes)
            dos.writeInt(config.getVoiceServer().getPort());  // serverPort (4 bytes)
            writeUUID(dos, player.getUuid());  // playerUUID (16 bytes)
            dos.writeByte(0);  // codec ordinal - VOIP=0 (1 byte)
            dos.writeInt(1024);  // mtuSize (4 bytes)
            dos.writeDouble(48.0);  // voiceChatDistance (8 bytes)
            dos.writeInt(1000);  // keepAlive (4 bytes)
            dos.writeBoolean(true);  // groupsEnabled (1 byte)

            // voiceHost - 关键字段，用于传递独立服务器地址
            String voiceHost = config.getVoiceServer().getHost() + ":" + config.getVoiceServer().getPort();
            writeMinecraftString(dos, voiceHost);  // voiceHost (variable length)

            dos.writeBoolean(true);  // allowRecording (1 byte)

            // 不写入任何额外字段，保持与原版客户端完全兼容

            byte[] data = baos.toByteArray();
            LOGGER.info("Sending SecretPacket to {}: {} bytes, voiceHost={}",
                       player.getName().getString(), data.length, voiceHost);

            // 发送到客户端
            player.getServer().execute(() -> {
                // TODO: 实现 Fabric 网络数据包发送
                LOGGER.info("SecretPacket would be sent to player: {} ({} bytes)", player.getName().getString(), data.length);
            });

        } finally {
            dos.close();
            baos.close();
        }
    }

    /**
     * 写入Minecraft格式的字符串（使用VarInt长度前缀）
     */
    private void writeMinecraftString(DataOutputStream dos, String str) throws IOException {
        byte[] bytes = str.getBytes("UTF-8");
        writeVarInt(dos, bytes.length);  // 写入长度（VarInt格式）
        dos.write(bytes);  // 写入字符串字节
    }

    /**
     * 写入VarInt（Minecraft网络协议格式）
     */
    private void writeVarInt(DataOutputStream dos, int value) throws IOException {
        while ((value & 0xFFFFFF80) != 0) {
            dos.writeByte((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        dos.writeByte(value & 0x7F);
    }
    
    /**
     * 写入UUID到数据流
     */
    private void writeUUID(DataOutputStream dos, UUID uuid) throws IOException {
        dos.writeLong(uuid.getMostSignificantBits());
        dos.writeLong(uuid.getLeastSignificantBits());
    }

}
