package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.model.Position;
import de.maxhenkel.voicechat.standalone.model.Permission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家数据管理器
 */
public class PlayerManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerManager.class);
    
    private final ConcurrentHashMap<UUID, PlayerData> players = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Set<UUID>> serverPlayers = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<UUID, UUID> playerSecrets = new ConcurrentHashMap<>();
    
    /**
     * 添加或更新玩家
     */
    public void addPlayer(PlayerData player) {
        PlayerData existing = players.get(player.getUuid());
        if (existing != null) {
            // 检查是否是服务器切换
            String oldServerName = existing.getServerName();
            String newServerName = player.getServerName();

            if (!oldServerName.equals(newServerName)) {
                LOGGER.info("Player {} switching from server {} to server {}",
                           player.getName(), oldServerName, newServerName);

                // 从旧服务器的玩家列表中移除
                Set<UUID> oldServerPlayerSet = serverPlayers.get(oldServerName);
                if (oldServerPlayerSet != null) {
                    oldServerPlayerSet.remove(player.getUuid());
                    if (oldServerPlayerSet.isEmpty()) {
                        serverPlayers.remove(oldServerName);
                    }
                }

                // 如果玩家在群组中，需要通知群组成员服务器变更
                if (existing.hasGroup()) {
                    // 这里可以添加群组通知逻辑
                    LOGGER.debug("Player {} in group {} switched servers",
                               player.getName(), existing.getGroupId());
                }
            }

            // 更新现有玩家数据
            existing.setOnline(player.isOnline());
            existing.setServerName(newServerName);
            existing.setName(player.getName()); // 更新名称（可能会变化）
            existing.setPermissions(player.getPermissions()); // 更新权限
            if (player.getPosition() != null) {
                existing.setPosition(player.getPosition());
            }
        } else {
            players.put(player.getUuid(), player);
            LOGGER.info("New player {} added from server {}", player.getName(), player.getServerName());
        }

        // 更新服务器玩家列表
        serverPlayers.computeIfAbsent(player.getServerName(), k -> ConcurrentHashMap.newKeySet())
                .add(player.getUuid());
    }
    
    /**
     * 移除玩家
     */
    public void removePlayer(UUID playerUuid) {
        PlayerData player = players.remove(playerUuid);
        if (player != null) {
            // 从服务器玩家列表中移除
            Set<UUID> serverPlayerSet = serverPlayers.get(player.getServerName());
            if (serverPlayerSet != null) {
                serverPlayerSet.remove(playerUuid);
                if (serverPlayerSet.isEmpty()) {
                    serverPlayers.remove(player.getServerName());
                }
            }
        }
    }
    
    /**
     * 获取玩家数据
     */
    public PlayerData getPlayer(UUID playerUuid) {
        return players.get(playerUuid);
    }
    
    /**
     * 根据名称获取玩家
     */
    public PlayerData getPlayerByName(String name) {
        return players.values().stream()
                .filter(player -> player.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取所有玩家
     */
    public Collection<PlayerData> getAllPlayers() {
        return players.values();
    }
    
    /**
     * 获取在线玩家
     */
    public Collection<PlayerData> getOnlinePlayers() {
        return players.values().stream()
                .filter(PlayerData::isOnline)
                .toList();
    }
    
    /**
     * 获取指定服务器的玩家
     */
    public Collection<PlayerData> getPlayersOnServer(String serverName) {
        Set<UUID> playerUuids = serverPlayers.get(serverName);
        if (playerUuids == null) {
            return Collections.emptyList();
        }
        
        return playerUuids.stream()
                .map(players::get)
                .filter(Objects::nonNull)
                .toList();
    }
    
    /**
     * 更新玩家位置
     */
    public void updatePlayerPosition(UUID playerUuid, Position position) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setPosition(position);
        }
    }
    
    /**
     * 更新玩家权限
     */
    public void updatePlayerPermissions(UUID playerUuid, Set<Permission> permissions) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setPermissions(permissions);
        }
    }
    
    /**
     * 设置玩家在线状态
     */
    public void setPlayerOnline(UUID playerUuid, boolean online) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setOnline(online);
            if (!online) {
                player.setVoiceConnected(false);
            }
        }
    }
    
    /**
     * 设置玩家语音连接状态
     */
    public void setPlayerVoiceConnected(UUID playerUuid, boolean connected) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setVoiceConnected(connected);
        }
    }
    
    /**
     * 设置玩家语音禁用状态
     */
    public void setPlayerVoiceDisabled(UUID playerUuid, boolean disabled) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setVoiceDisabled(disabled);
        }
    }
    
    /**
     * 设置玩家群组
     */
    public void setPlayerGroup(UUID playerUuid, UUID groupId) {
        PlayerData player = players.get(playerUuid);
        if (player != null) {
            player.setGroupId(groupId);
        }
    }
    
    /**
     * 获取指定范围内的玩家
     */
    public Collection<PlayerData> getPlayersInRange(Position center, double range, String worldId) {
        return players.values().stream()
                .filter(player -> player.isOnline() && player.getPosition() != null)
                .filter(player -> worldId.equals(player.getPosition().getWorldId()))
                .filter(player -> player.getPosition().isWithinRange(center, range))
                .toList();
    }
    
    /**
     * 获取玩家统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPlayers", players.size());
        stats.put("onlinePlayers", getOnlinePlayers().size());
        stats.put("voiceConnectedPlayers", players.values().stream()
                .mapToInt(player -> player.isVoiceConnected() ? 1 : 0)
                .sum());
        stats.put("servers", serverPlayers.keySet());
        return stats;
    }
    
    /**
     * 清理过期的玩家数据
     */
    public void cleanupExpiredPlayers(long maxOfflineTime) {
        long currentTime = System.currentTimeMillis();
        
        players.entrySet().removeIf(entry -> {
            PlayerData player = entry.getValue();
            if (!player.isOnline() && 
                (currentTime - player.getLastUpdate()) > maxOfflineTime) {
                
                // 从服务器玩家列表中移除
                Set<UUID> serverPlayerSet = serverPlayers.get(player.getServerName());
                if (serverPlayerSet != null) {
                    serverPlayerSet.remove(entry.getKey());
                    if (serverPlayerSet.isEmpty()) {
                        serverPlayers.remove(player.getServerName());
                    }
                }

                return true;
            }
            return false;
        });
    }

    /**
     * 设置玩家密钥
     */
    public void setPlayerSecret(UUID playerUuid, UUID secret) {
        playerSecrets.put(playerUuid, secret);
    }

    /**
     * 获取玩家密钥
     */
    public UUID getPlayerSecret(UUID playerUuid) {
        return playerSecrets.get(playerUuid);
    }

    /**
     * 移除玩家密钥
     */
    public void removePlayerSecret(UUID playerUuid) {
        playerSecrets.remove(playerUuid);
    }

    /**
     * 根据网络地址查找玩家UUID（用于数据包解密）
     */
    public UUID findPlayerByAddress(String address) {
        // 这是一个简化的实现，实际项目中可能需要更复杂的地址映射
        // 暂时返回null，需要在连接建立时维护地址映射
        return null;
    }
}
