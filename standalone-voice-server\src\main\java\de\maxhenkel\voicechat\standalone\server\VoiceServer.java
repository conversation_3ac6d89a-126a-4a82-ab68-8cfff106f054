package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.config.ServerConfig;
import de.maxhenkel.voicechat.standalone.audio.OpusManager;
import de.maxhenkel.voicechat.standalone.model.PlayerData;
import de.maxhenkel.voicechat.standalone.model.Position;
import de.maxhenkel.voicechat.standalone.network.NetworkMessage;
import de.maxhenkel.voicechat.standalone.network.Packet;
import de.maxhenkel.voicechat.standalone.network.packets.*;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 独立语音服务器核心类
 */
public class VoiceServer {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceServer.class);
    
    private final ServerConfig config;
    private final PlayerManager playerManager;
    private final GroupManager groupManager;
    private final ConnectionManager connectionManager;
    private final PermissionManager permissionManager;
    private final WorldManager worldManager;
    private final OpusManager opusManager;
    private final ServerRegistrationManager serverRegistrationManager;
    
    private EventLoopGroup group;
    private Channel channel;
    private ScheduledExecutorService scheduler;
    
    // 客户端连接映射
    private final ConcurrentHashMap<UUID, ClientConnection> connections = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<SocketAddress, UUID> addressToPlayer = new ConcurrentHashMap<>();

    // 广播管理
    private final Set<UUID> activeBroadcasters = ConcurrentHashMap.newKeySet();
    
    public VoiceServer(ServerConfig config) {
        this.config = config;
        this.playerManager = new PlayerManager();
        this.groupManager = new GroupManager();
        this.connectionManager = new ConnectionManager(this);
        this.permissionManager = new PermissionManager();
        this.worldManager = new WorldManager(permissionManager);
        this.opusManager = OpusManager.fromConfig(config.getVoice().getCodec());
        this.serverRegistrationManager = new ServerRegistrationManager(this, permissionManager, worldManager);
    }
    
    /**
     * 启动语音服务器
     */
    public void start() throws Exception {
        LOGGER.info("Starting voice server...");
        
        group = new NioEventLoopGroup();
        scheduler = Executors.newScheduledThreadPool(2);
        
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioDatagramChannel.class)
                .option(ChannelOption.SO_BROADCAST, true)
                .handler(new ChannelInitializer<NioDatagramChannel>() {
                    @Override
                    protected void initChannel(NioDatagramChannel ch) {
                        ch.pipeline().addLast(new VoiceServerHandler());
                    }
                });
        
        String bindAddress = config.getServer().getBindAddress();
        if (bindAddress.isEmpty()) {
            bindAddress = config.getServer().getHost();
        }
        
        channel = bootstrap.bind(bindAddress, config.getServer().getPort()).sync().channel();
        
        // 启动定时任务
        startScheduledTasks();

        // 启动服务器注册管理器
        serverRegistrationManager.start();

        LOGGER.info("Voice server started on {}:{}", bindAddress, config.getServer().getPort());
    }
    
    /**
     * 停止语音服务器
     */
    public void stop() throws Exception {
        LOGGER.info("Stopping voice server...");

        // 停止服务器注册管理器
        if (serverRegistrationManager != null) {
            serverRegistrationManager.stop();
        }

        if (scheduler != null) {
            scheduler.shutdown();
        }

        if (channel != null) {
            channel.close().sync();
        }

        if (group != null) {
            group.shutdownGracefully().sync();
        }

        LOGGER.info("Voice server stopped");
    }
    
    /**
     * 启动定时任务
     */
    private void startScheduledTasks() {
        // 心跳检查任务
        scheduler.scheduleAtFixedRate(this::checkKeepAlive, 
                config.getVoice().getKeepAlive(), 
                config.getVoice().getKeepAlive(), 
                TimeUnit.MILLISECONDS);
        
        // 连接超时检查任务
        scheduler.scheduleAtFixedRate(this::checkTimeouts, 5000, 5000, TimeUnit.MILLISECONDS);

        // 密钥清理任务（每5分钟执行一次）
        scheduler.scheduleAtFixedRate(this::cleanupSecrets, 300000, 300000, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 发送心跳包
     */
    private void checkKeepAlive() {
        // KeepAlivePacket不包含时间戳数据（与原项目一致）
        KeepAlivePacket keepAlive = new KeepAlivePacket();

        connections.values().forEach(connection -> {
            try {
                sendPacket(keepAlive, connection.getAddress());
            } catch (Exception e) {
                LOGGER.warn("Failed to send keep alive to {}", connection.getAddress(), e);
            }
        });
    }
    
    /**
     * 检查连接超时
     */
    private void checkTimeouts() {
        long currentTime = System.currentTimeMillis();
        long timeout = config.getVoice().getKeepAlive() * 10; // 10倍心跳间隔作为超时时间
        
        connections.entrySet().removeIf(entry -> {
            ClientConnection connection = entry.getValue();
            if (currentTime - connection.getLastKeepAlive() > timeout) {
                LOGGER.info("Client {} timed out", entry.getKey());
                addressToPlayer.remove(connection.getAddress());
                playerManager.setPlayerVoiceConnected(entry.getKey(), false);
                return true;
            }
            return false;
        });
    }

    /**
     * 清理离线玩家的密钥
     */
    private void cleanupSecrets() {
        try {
            connectionManager.cleanupOfflinePlayerSecrets();
            LOGGER.debug("Cleaned up offline player secrets");
        } catch (Exception e) {
            LOGGER.warn("Failed to cleanup secrets", e);
        }
    }

    /**
     * 发送数据包到指定地址
     */
    public void sendPacket(Packet<?> packet, SocketAddress address) throws Exception {
        // 获取目标玩家UUID
        UUID targetPlayerUuid = addressToPlayer.get(address);
        if (targetPlayerUuid == null) {
            LOGGER.warn("Cannot send packet to unknown address: {}", address);
            return;
        }

        // 获取目标玩家的密钥
        UUID secret = playerManager.getPlayerSecret(targetPlayerUuid);
        if (secret == null) {
            LOGGER.warn("Cannot send packet to player {} - no secret", targetPlayerUuid);
            return;
        }

        // 创建网络消息并加密
        NetworkMessage message = new NetworkMessage(packet, address);
        byte[] data = message.toBytes(targetPlayerUuid, secret);

        DatagramPacket datagramPacket = new DatagramPacket(
                Unpooled.wrappedBuffer(data),
                (InetSocketAddress) address
        );

        channel.writeAndFlush(datagramPacket);
    }
    
    /**
     * 处理接收到的网络消息
     */
    private void handleMessage(NetworkMessage message) {
        Packet<?> packet = message.getPacket();
        SocketAddress address = message.getAddress();

        if (packet instanceof AuthenticatePacket) {
            handleAuthenticate((AuthenticatePacket) packet, address);
        } else if (packet instanceof MicPacket) {
            handleMicPacket((MicPacket) packet, address);
        } else if (packet instanceof KeepAlivePacket) {
            handleKeepAlive((KeepAlivePacket) packet, address);
        } else if (packet instanceof PingPacket) {
            handlePing((PingPacket) packet, address);
        } else if (packet instanceof ConnectionCheckPacket) {
            handleConnectionCheck((ConnectionCheckPacket) packet, address);
        } else if (packet instanceof PlayerSoundPacket) {
            handlePlayerSound((PlayerSoundPacket) packet, address);
        } else if (packet instanceof GroupSoundPacket) {
            handleGroupSound((GroupSoundPacket) packet, address);
        } else if (packet instanceof LocationSoundPacket) {
            handleLocationSound((LocationSoundPacket) packet, address);
        }
    }
    
    /**
     * 处理客户端认证
     */
    private void handleAuthenticate(AuthenticatePacket packet, SocketAddress address) {
        UUID playerUuid = packet.getPlayerUuid();
        UUID secret = packet.getSecret();

        // 验证玩家和密钥
        PlayerData player = playerManager.getPlayer(playerUuid);
        if (player == null || !player.isOnline()) {
            LOGGER.warn("Authentication failed for unknown player: {}", playerUuid);
            return;
        }

        // 验证密钥
        UUID expectedSecret = playerManager.getPlayerSecret(playerUuid);
        if (expectedSecret == null || !expectedSecret.equals(secret)) {
            LOGGER.warn("Authentication failed for player {} - invalid secret", player.getName());
            return;
        }

        // 创建连接
        ClientConnection connection = new ClientConnection(playerUuid, address);
        connections.put(playerUuid, connection);
        addressToPlayer.put(address, playerUuid);

        // 更新玩家状态
        playerManager.setPlayerVoiceConnected(playerUuid, true);

        LOGGER.info("Player {} authenticated from {}", player.getName(), address);

        // 发送认证确认包
        try {
            AuthenticateAckPacket ackPacket = new AuthenticateAckPacket();
            sendPacket(ackPacket, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send authentication ack to {}", address, e);
        }
    }
    
    /**
     * 处理麦克风音频包
     */
    private void handleMicPacket(MicPacket packet, SocketAddress address) {
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received mic packet from unauthenticated address: {}", address);
            return;
        }
        
        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }

        // 检查语音权限
        if (!permissionManager.hasPermission(sender, de.maxhenkel.voicechat.standalone.model.Permission.SPEAK)) {
            LOGGER.debug("Player {} does not have speak permission", sender.getName());
            return;
        }

        // 简单验证音频数据（与原项目保持一致，不进行严格的大小验证）
        if (packet.getData() == null || packet.getData().length == 0) {
     //       LOGGER.debug("Rejecting null or empty audio packet from player {}", sender.getName());
            return;
        }

        // 可选：处理音频数据（目前直接使用原始数据，与原项目行为一致）
        byte[] processedAudioData = opusManager.processAudioPacket(packet.getData());
        if (processedAudioData != null && processedAudioData != packet.getData()) {
            packet = new MicPacket(sender.getUuid(), processedAudioData, packet.isWhispering(), packet.getSequenceNumber());
        }

        // 首先检查是否是广播玩家
        if (isBroadcasting(sender.getUuid())) {
            processBroadcastVoiceData(sender, packet);
            return;
        }

        // 处理语音数据（支持群组语音和群组类型）
        if (sender.getGroupId() != null) {
            // 检查群组权限
            if (!permissionManager.hasPermission(sender, de.maxhenkel.voicechat.standalone.model.Permission.GROUPS)) {
                LOGGER.debug("Player {} does not have group permission", sender.getName());
                processVoiceData(sender, packet); // 降级为普通语音
                return;
            }
            processGroupVoiceData(sender, packet);
        } else {
            processVoiceData(sender, packet);
        }
    }
    
    /**
     * 处理心跳包
     */
    private void handleKeepAlive(KeepAlivePacket packet, SocketAddress address) {
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid != null) {
            ClientConnection connection = connections.get(playerUuid);
            if (connection != null) {
                connection.updateKeepAlive();
            }
        }
    }

    /**
     * 处理Ping包
     */
    private void handlePing(PingPacket packet, SocketAddress address) {
        try {
            // 响应Ping请求（返回相同的时间戳）
            PingPacket response = new PingPacket(packet.getTimestamp());
            sendPacket(response, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send ping response to {}", address, e);
        }
    }

    /**
     * 处理连接检查包
     */
    private void handleConnectionCheck(ConnectionCheckPacket packet, SocketAddress address) {
        try {
            // 响应连接检查
            ConnectionCheckAckPacket ackPacket = new ConnectionCheckAckPacket();
            sendPacket(ackPacket, address);
        } catch (Exception e) {
            LOGGER.warn("Failed to send connection check ack to {}", address, e);
        }
    }

    /**
     * 处理玩家音频包
     */
    private void handlePlayerSound(PlayerSoundPacket packet, SocketAddress address) {
        // 玩家音频包通常是服务器发送给客户端的，客户端不应该发送这种包
    }

    /**
     * 处理群组音频包
     */
    private void handleGroupSound(GroupSoundPacket packet, SocketAddress address) {
        // 群组音频包通常是服务器发送给客户端的，客户端不应该发送这种包
        // 但如果客户端发送，我们可以将其转发给群组成员
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received group sound packet from unauthenticated address: {}", address);
            return;
        }

        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }

        // 广播给群组成员
        broadcastToGroupMembers(sender, packet);
    }

    /**
     * 处理位置音频包
     */
    private void handleLocationSound(LocationSoundPacket packet, SocketAddress address) {
        // 位置音频包通常是服务器发送给客户端的，客户端不应该发送这种包
        // 但如果客户端发送，我们可以将其转发给附近的玩家
        UUID playerUuid = addressToPlayer.get(address);
        if (playerUuid == null) {
            LOGGER.warn("Received location sound packet from unauthenticated address: {}", address);
            return;
        }

        PlayerData sender = playerManager.getPlayer(playerUuid);
        if (sender == null) {
            return;
        }

        // 广播给指定位置附近的玩家
        broadcastToLocationNearbyPlayers(sender, packet);
    }
    
    /**
     * 处理语音数据
     */
    private void processVoiceData(PlayerData sender, MicPacket micPacket) {
        // 使用发送者的UUID作为channelId，这样客户端可以正确识别说话者
        UUID channelId = sender.getUuid();

        // 检查是否是观察者模式
        if (sender.isSpectator()) {
            // 观察者使用LocationSoundPacket，从其位置发出声音
            // 权限检查在广播时进行，这里允许观察者发言
            if (sender.getPosition() != null) {
                Position pos = sender.getPosition();
                LocationSoundPacket locationPacket = new LocationSoundPacket(
                        channelId,          // channelId - 使用发送者UUID
                        sender.getUuid(),   // sender
                        micPacket.getData(), // 音频数据
                        micPacket.getSequenceNumber(), // 序列号
                        (float) config.getVoice().getMaxDistance(), // 距离
                        "spectator", // 类别
                        pos.getX(), pos.getY(), pos.getZ() // 位置坐标
                );
                broadcastLocationSound(sender, locationPacket);
                return;
            } else {
                LOGGER.warn("Spectator {} has no position, using PlayerSoundPacket", sender.getName());
            }
        }

        // 普通玩家或没有位置的观察者使用PlayerSoundPacket
        PlayerSoundPacket soundPacket = new PlayerSoundPacket(
                channelId,          // channelId - 使用发送者UUID
                sender.getUuid(),   // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                micPacket.isWhispering(), // 是否耳语
                (float) config.getVoice().getMaxDistance(), // 距离
                sender.isSpectator() ? "spectator" : "proximity" // 类别
        );

        // 广播给附近的玩家
        broadcastToNearbyPlayers(sender, soundPacket, micPacket.isWhispering());
    }
    
    /**
     * 广播音频到附近玩家（考虑群组类型）
     */
    private void broadcastToNearbyPlayers(PlayerData sender, PlayerSoundPacket soundPacket, boolean whispering) {
        // 获取发送者的世界ID
        String worldId = null;
        if (sender.getPosition() != null) {
            worldId = sender.getPosition().getWorldId();
        }

        // 如果没有位置信息，使用默认距离
        double maxDistance = 48.0; // 默认距离
        if (worldId != null) {
            // 从带前缀的世界ID中提取原始世界名
            String worldName = extractWorldName(worldId);
            maxDistance = worldManager.getMaxVoiceDistance(worldName);
        }

        if (whispering) {
            maxDistance *= 0.5; // 悄悄话距离减半
        }

        // 获取发送者的群组信息
        UUID senderGroupId = sender.getGroupId();
        GroupManager.VoiceGroup senderGroup = null;
        if (senderGroupId != null) {
            senderGroup = groupManager.getGroup(senderGroupId);
        }

        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue; // 跳过未连接或禁用语音的玩家
            }

            // 使用世界管理器检查是否可以通信
            if (!worldManager.canCommunicate(sender, receiver)) {
                continue; // 不能通信（跨世界/跨服务器限制）
            }

            // 如果在同一世界，检查距离
            if (sender.isInSameWorld(receiver)) {
                double distance = sender.getDistanceTo(receiver);
                if (!sender.isWithinVoiceRange(receiver, maxDistance)) {
                    continue; // 超出语音范围
                }
            } else {
                continue;
            }

            // 观察者权限检查：观察者之间可以互相听到，观察者对普通玩家需要权限
            if (sender.isSpectator() && !receiver.isSpectator()) {
                // 观察者 -> 普通玩家：需要权限
                if (!permissionManager.hasPermission(sender, de.maxhenkel.voicechat.standalone.model.Permission.SPECTATOR_INTERACTION)) {
                    continue;
                }
            }

            // 检查群组规则
            UUID receiverGroupId = receiver.getGroupId();

            // 如果发送者在群组中
            if (senderGroup != null) {
                // 如果接收者也在同一个群组中，跳过（群组内通信由群组处理）
                if (senderGroupId.equals(receiverGroupId)) {
                    continue;
                }

                // 如果发送者在ISOLATED群组中，不能与群组外的人通信
                if (senderGroup.getType().isIsolated()) {
                    continue;
                }

                // 如果发送者在NORMAL群组中，群组外的人听不到
                if (senderGroup.getType().isNormal()) {
                    continue;
                }

                // 只有OPEN群组可以让群组外的人听到
            }

            // 如果接收者在群组中，检查接收者的群组类型
            if (receiverGroupId != null) {
                GroupManager.VoiceGroup receiverGroup = groupManager.getGroup(receiverGroupId);
                if (receiverGroup != null) {
                    // 如果接收者在ISOLATED群组中，不能听到群组外的声音
                    if (receiverGroup.getType().isIsolated()) {
                        continue;
                    }
                    // NORMAL和OPEN群组的成员可以听到群组外的声音
                }
            }

            // 发送音频包
            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    /**
     * 广播群组音频到群组成员
     */
    private void broadcastToGroupMembers(PlayerData sender, GroupSoundPacket soundPacket) {
        // 获取发送者所在的群组
        UUID groupId = sender.getGroupId();
        if (groupId == null) {
            return;
        }

        GroupManager.VoiceGroup group = groupManager.getGroup(groupId);
        if (group == null) {
            return;
        }

        // 广播给群组中的其他成员
        int sentCount = 0;
        for (UUID memberId : group.getMembers()) {
            if (memberId.equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            PlayerData member = playerManager.getPlayer(memberId);
            if (member == null || !member.isVoiceConnected() || member.isVoiceDisabled()) {
                continue;
            }

            // 检查是否可以通信（跨世界/跨服务器权限）
            if (!worldManager.canCommunicate(sender, member)) {
                continue; // 不能通信
            }

            ClientConnection connection = connections.get(memberId);
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                    sentCount++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to send group sound packet to {}", member.getName(), e);
                }
            }
        }
    }

    /**
     * 广播位置音频到指定位置附近的玩家
     */
    private void broadcastToLocationNearbyPlayers(PlayerData sender, LocationSoundPacket soundPacket) {
        double soundX = soundPacket.getX();
        double soundY = soundPacket.getY();
        double soundZ = soundPacket.getZ();
        double maxDistance = soundPacket.getDistance();

        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue;
            }

            if (!sender.isInSameWorld(receiver)) {
                continue; // 不在同一世界
            }

            // 计算到声音位置的距离
            if (receiver.getPosition() == null) {
                continue; // 没有位置信息
            }

            double distance = Math.sqrt(
                Math.pow(receiver.getPosition().getX() - soundX, 2) +
                Math.pow(receiver.getPosition().getY() - soundY, 2) +
                Math.pow(receiver.getPosition().getZ() - soundZ, 2)
            );

            if (distance > maxDistance) {
                continue; // 超出音频范围
            }

            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send location sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    /**
     * 支持群组语音的语音数据处理（根据群组类型）
     */
    private void processGroupVoiceData(PlayerData sender, MicPacket micPacket) {
        UUID groupId = sender.getGroupId();
        if (groupId == null) {
            // 如果不在群组中，使用普通的附近语音
            processVoiceData(sender, micPacket);
            return;
        }

        GroupManager.VoiceGroup group = groupManager.getGroup(groupId);
        if (group == null) {
            LOGGER.warn("Player {} is in non-existent group {}", sender.getName(), groupId);
            processVoiceData(sender, micPacket);
            return;
        }

        // 创建群组音频包（与原版一致：使用发送者UUID作为channelId）
        GroupSoundPacket groupSoundPacket = new GroupSoundPacket(
                sender.getUuid(), // channelId使用发送者UUID（与原版一致）
                sender.getUuid(), // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                "group" // 类别
        );



        // 根据群组类型决定广播策略
        switch (group.getType()) {
            case NORMAL:
                // 普通群组：广播给群组成员 + 附近非群组玩家（但非群组玩家听不到群组内的声音）
                broadcastToGroupMembers(sender, groupSoundPacket);
                // 注意：NORMAL模式下，群组成员可以听到附近非群组玩家的声音，
                // 但这是在非群组玩家说话时处理的，不是在这里
                break;

            case OPEN:
                // 开放群组：广播给群组成员 + 附近非群组玩家（双向通信）
                broadcastToGroupMembers(sender, groupSoundPacket);
                broadcastToNearbyNonGroupPlayers(sender, micPacket);
                break;

            case ISOLATED:
                // 隔离群组：只广播给群组成员，完全隔离
                broadcastToGroupMembers(sender, groupSoundPacket);
                break;

            default:
                LOGGER.warn("Unknown group type: {} for group {}", group.getType(), groupId);
                broadcastToGroupMembers(sender, groupSoundPacket);
                break;
        }
    }

    /**
     * 广播给附近的非群组玩家（用于开放群组）
     */
    private void broadcastToNearbyNonGroupPlayers(PlayerData sender, MicPacket micPacket) {
        // 获取世界特定的最大距离
        double worldMaxDistance = worldManager.getMaxVoiceDistance(sender.getWorldName());

        // 创建玩家音频包
        PlayerSoundPacket soundPacket = new PlayerSoundPacket(
                UUID.randomUUID(), // channelId - 可以是默认频道
                sender.getUuid(),   // sender
                micPacket.getData(), // 音频数据
                micPacket.getSequenceNumber(), // 序列号
                micPacket.isWhispering(), // 是否耳语
                (float) worldMaxDistance, // 使用世界特定距离
                "proximity" // 类别
        );

        // 广播给附近的非群组玩家
        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue;
            }

            // 使用世界管理器检查是否可以通信
            if (!worldManager.canCommunicate(sender, receiver)) {
                continue; // 不能通信（跨世界/跨服务器限制）
            }

            // 跳过群组成员（他们已经通过群组音频包收到了）
            if (receiver.getGroupId() != null && receiver.getGroupId().equals(sender.getGroupId())) {
                continue;
            }

            // 如果在同一世界，检查距离
            if (sender.isInSameWorld(receiver)) {
                if (sender.getPosition() != null && receiver.getPosition() != null) {
                    double distance = sender.getPosition().getDistanceTo(receiver.getPosition());
                    double maxDistance = micPacket.isWhispering() ? worldMaxDistance * 0.5 : worldMaxDistance;

                    if (distance > maxDistance) {
                        continue; // 超出语音范围
                    }
                }
            }

            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    // Getters
    public ServerConfig getConfig() { return config; }
    public PlayerManager getPlayerManager() { return playerManager; }
    public GroupManager getGroupManager() { return groupManager; }
    public ConnectionManager getConnectionManager() { return connectionManager; }
    public PermissionManager getPermissionManager() { return permissionManager; }
    public WorldManager getWorldManager() { return worldManager; }
    public OpusManager getOpusManager() { return opusManager; }

    /**
     * 检查玩家是否连接到语音聊天
     */
    public boolean isPlayerConnected(UUID playerUuid) {
        return connections.containsKey(playerUuid);
    }

    /**
     * 断开玩家的语音连接
     */
    public void disconnectPlayer(UUID playerUuid) {
        ClientConnection connection = connections.remove(playerUuid);
        if (connection != null) {
            // 从地址映射中移除
            addressToPlayer.remove(connection.getAddress());

            // 设置玩家语音连接状态为断开
            playerManager.setPlayerVoiceConnected(playerUuid, false);

            // 移除玩家的认证密钥
            connectionManager.removeSecret(playerUuid);

            // 停止广播（如果正在广播）
            stopBroadcast(playerUuid);

            LOGGER.info("Disconnected player {} from voice chat", playerUuid);
        }
    }

    /**
     * 获取连接映射（用于ConnectionManager）
     */
    public ConcurrentHashMap<UUID, ClientConnection> getConnections() {
        return connections;
    }
    
    /**
     * 语音服务器网络处理器
     */
    private class VoiceServerHandler extends SimpleChannelInboundHandler<DatagramPacket> {
        
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) {
            try {
                byte[] data = new byte[packet.content().readableBytes()];
                packet.content().readBytes(data);
                
                NetworkMessage message = NetworkMessage.fromBytes(data, packet.sender(), playerManager);
                if (message != null) {
                    handleMessage(message);
                }
                // 如果message为null，说明是未知数据包，已经在NetworkMessage中记录了警告
                
            } catch (Exception e) {
                LOGGER.warn("Failed to process packet from {}", packet.sender(), e);
            }
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            LOGGER.error("Voice server handler error", cause);
        }
    }

    /**
     * 检查玩家是否正在广播
     */
    public boolean isBroadcasting(UUID playerUuid) {
        return activeBroadcasters.contains(playerUuid);
    }

    /**
     * 开始广播
     */
    public void startBroadcast(UUID playerUuid) {
        activeBroadcasters.add(playerUuid);
        LOGGER.info("Player {} started broadcasting", playerUuid);
    }

    /**
     * 停止广播
     */
    public void stopBroadcast(UUID playerUuid) {
        activeBroadcasters.remove(playerUuid);
        LOGGER.info("Player {} stopped broadcasting", playerUuid);
    }

    /**
     * 处理广播语音数据
     */
    private void processBroadcastVoiceData(PlayerData sender, MicPacket micPacket) {
        // 广播给所有在线玩家，为每个接收者创建LocationSoundPacket
        broadcastToAllPlayers(sender, micPacket);
    }

    /**
     * 广播给同服务器的在线玩家（使用LocationSoundPacket）
     */
    private void broadcastToAllPlayers(PlayerData sender, MicPacket micPacket) {
        int sentCount = 0;
        int totalPlayers = 0;
        String senderServerName = sender.getServerName();

        for (PlayerData receiver : playerManager.getAllPlayers()) {
            // 只统计同服务器的玩家
            if (senderServerName.equals(receiver.getServerName())) {
                totalPlayers++;
            }

            // 不发送给自己
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue;
            }

            // 只广播给同一服务器的玩家
            if (!senderServerName.equals(receiver.getServerName())) {
                continue;
            }

            // 检查玩家是否在线
            if (!receiver.isOnline()) {
                continue;
            }

            // 检查语音连接状态
            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue;
            }

            // 检查接收者位置
            if (receiver.getPosition() == null) {
                continue;
            }

            // 为每个接收者创建LocationSoundPacket，位置在接收者附近
            Position receiverPos = receiver.getPosition();
            LocationSoundPacket locationPacket = new LocationSoundPacket(
                    sender.getUuid(),           // channelId - 使用发送者UUID
                    sender.getUuid(),           // sender
                    micPacket.getData(),        // 音频数据
                    micPacket.getSequenceNumber(), // 序列号
                    Float.MAX_VALUE,            // 无限距离
                    "plugin",                   // 使用plugin类别（广播属于插件功能）
                    receiverPos.getX(),         // 接收者的X坐标
                    receiverPos.getY(),         // 接收者的Y坐标
                    receiverPos.getZ()          // 接收者的Z坐标
            );

            // 发送音频包
            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(locationPacket, connection.getAddress());
                    sentCount++;
                } catch (Exception e) {
                    LOGGER.warn("Failed to send broadcast packet to {}", receiver.getName(), e);
                }
            }
        }

        LOGGER.info("Broadcast from {} (server: {}): sent to {}/{} players on same server",
                   sender.getName(), senderServerName, sentCount, totalPlayers - 1);
    }

    /**
     * 从带服务器前缀的世界ID中提取原始世界名
     * 例如: "server1:world" -> "world"
     */
    private String extractWorldName(String worldId) {
        if (worldId == null) {
            return null;
        }

        int colonIndex = worldId.indexOf(':');
        if (colonIndex > 0 && colonIndex < worldId.length() - 1) {
            return worldId.substring(colonIndex + 1);
        }

        // 如果没有冒号，返回原始字符串
        return worldId;
    }

    /**
     * 广播位置音频（用于观察者）
     */
    private void broadcastLocationSound(PlayerData sender, LocationSoundPacket soundPacket) {
        // 从LocationSoundPacket中获取位置信息
        double soundX = soundPacket.getX();
        double soundY = soundPacket.getY();
        double soundZ = soundPacket.getZ();
        double maxDistance = soundPacket.getDistance();

        for (PlayerData receiver : playerManager.getAllPlayers()) {
            if (receiver.getUuid().equals(sender.getUuid())) {
                continue; // 不发送给自己
            }

            if (!receiver.isVoiceConnected() || receiver.isVoiceDisabled()) {
                continue; // 跳过未连接或禁用语音的玩家
            }

            // 使用世界管理器检查是否可以通信
            if (!worldManager.canCommunicate(sender, receiver)) {
                continue;
            }

            // 观察者权限检查：观察者之间可以互相听到，观察者对普通玩家需要权限
            if (sender.isSpectator() && !receiver.isSpectator()) {
                // 观察者 -> 普通玩家：需要权限
                if (!permissionManager.hasPermission(sender, de.maxhenkel.voicechat.standalone.model.Permission.SPECTATOR_INTERACTION)) {
                    continue;
                }
            }

            // 检查距离（基于声音位置）
            if (receiver.getPosition() != null) {
                Position receiverPos = receiver.getPosition();
                double distance = Math.sqrt(
                    Math.pow(receiverPos.getX() - soundX, 2) +
                    Math.pow(receiverPos.getY() - soundY, 2) +
                    Math.pow(receiverPos.getZ() - soundZ, 2)
                );
                if (distance > maxDistance) {
                    continue;
                }
            }

            // 发送音频包
            ClientConnection connection = connections.get(receiver.getUuid());
            if (connection != null) {
                try {
                    sendPacket(soundPacket, connection.getAddress());
                } catch (Exception e) {
                    LOGGER.warn("Failed to send location sound packet to {}", receiver.getName(), e);
                }
            }
        }
    }

    // Getters
    public ServerRegistrationManager getServerRegistrationManager() {
        return serverRegistrationManager;
    }

}
