package de.maxhenkel.voicechat.adapter.fabric.model;

/**
 * 位置信息模型
 */
public class Position {
    
    private final String worldId;
    private final double x;
    private final double y;
    private final double z;
    
    public Position(String worldId, double x, double y, double z) {
        this.worldId = worldId;
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    // Getters
    public String getWorldId() { return worldId; }
    public double getX() { return x; }
    public double getY() { return y; }
    public double getZ() { return z; }
    
    /**
     * 计算到另一个位置的距离
     */
    public double getDistanceTo(Position other) {
        if (!worldId.equals(other.worldId)) {
            return Double.MAX_VALUE;
        }
        
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    @Override
    public String toString() {
        return "Position{" +
                "worldId='" + worldId + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", z=" + z +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Position position = (Position) o;
        
        if (Double.compare(position.x, x) != 0) return false;
        if (Double.compare(position.y, y) != 0) return false;
        if (Double.compare(position.z, z) != 0) return false;
        return worldId != null ? worldId.equals(position.worldId) : position.worldId == null;
    }
    
    @Override
    public int hashCode() {
        int result;
        long temp;
        result = worldId != null ? worldId.hashCode() : 0;
        temp = Double.doubleToLongBits(x);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(y);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        temp = Double.doubleToLongBits(z);
        result = 31 * result + (int) (temp ^ (temp >>> 32));
        return result;
    }
}
