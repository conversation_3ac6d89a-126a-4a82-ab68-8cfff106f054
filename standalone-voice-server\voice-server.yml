# 独立语音服务器配置文件

# 语音服务器配置
server:
  # 绑定地址，0.0.0.0 表示绑定所有网络接口
  host: "0.0.0.0"
  # UDP端口，用于语音数据传输
  port: 24454
  # 特定绑定地址，留空使用上面的host
  bind_address: ""

# API服务器配置
api:
  # API服务器地址
  host: "0.0.0.0"
  # API服务器端口
  port: 8080
  # 认证令牌，Minecraft服务器需要使用此令牌访问API
  auth_token: "change-this-secret-token"

# 语音配置
voice:
  # 音频编解码器：VOIP, AUDIO, RESTRICTED_LOWDELAY
  # VOIP: 针对语音通话优化，提供最佳的语音质量和低延迟
  # AUDIO: 针对音频内容优化，适用于音乐和高质量音频内容
  # RESTRICTED_LOWDELAY: 受限低延迟模式，提供最低延迟但可能牺牲音质
  codec: "VOIP"
  # 最大传输单元大小（字节）
  mtu_size: 1024
  # 心跳间隔（毫秒）
  keep_alive: 1000
  # 最大语音距离
  max_distance: 48.0
  # 耳语距离倍数（相对于最大距离）
  whisper_distance_multiplier: 0.5
  # 是否启用群组聊天
  groups_enabled: true
  # 是否允许录音
  allow_recording: true

  # Opus编解码器高级设置
  # 比特率（bps）- 控制音频质量和带宽使用
  # VOIP推荐: 24000, AUDIO推荐: 64000, RESTRICTED_LOWDELAY推荐: 16000
  bitrate: 24000
  # 复杂度（0-10）- 控制编码质量和CPU使用
  # 更高的复杂度提供更好的音质但消耗更多CPU
  complexity: 5
  # DTX（不连续传输）- 在静音时停止传输以节省带宽
  dtx_enabled: true
  # FEC（前向纠错）- 提高网络丢包时的音频质量
  fec_enabled: true

# 安全配置
security:
  # 是否启用加密
  encryption_enabled: true
  # 认证超时时间（毫秒）
  auth_timeout: 30000

# 跨服务器通信配置
cross-server:
  # 是否自动启用跨服务器通信（当服务器注册时自动配置）
  auto_enable: true
  # 默认是否允许跨服务器通信
  default_allowed: true
  # 服务器注册超时时间（毫秒）
  registration_timeout: 30000
  # Keepalive超时时间（毫秒）- 超过此时间未收到keepalive的服务器将被移除
  keepalive_timeout: 300000   # 5分钟（减少等待时间）
  # 清理间隔（毫秒）- 多久检查一次过期的服务器
  cleanup_interval: 120000    # 2分钟（更频繁的清理）
  # 是否在服务器注册时立即建立所有跨服务器规则
  immediate_rule_establishment: true

# 跨世界通信配置
cross-world:
  # 是否默认允许跨世界通信
  default_allowed: true
  # 世界配置超时时间（毫秒）
  config_timeout: 30000

# 权限管理配置
permissions:
  # 默认权限设置
  defaults:
    # 基本权限
    listen: true
    speak: true
    groups: true
    create_group: true
    join_group: true
    # 跨区域权限
    cross_world: true
    cross_server: true
    # 管理权限
    manage_group: false
    admin: false
    record: false
    # 观察者权限
    spectator_interaction: false
    spectator_possession: false

  # 权限组配置
  groups:
    # 管理员组
    admin:
      - "admin"
      - "manage_group"
      - "record"
      - "spectator_interaction"
      - "spectator_possession"
    # 版主组
    moderator:
      - "manage_group"
      - "record"
    # VIP组
    vip:
      - "cross_world"
      - "cross_server"
      - "create_group"

# 服务器管理配置
server-management:
  # 是否启用服务器自动注册
  auto_registration: true
  # 是否在服务器注册时自动配置跨服务器通信
  auto_configure_cross_server: true
  # 服务器名称验证规则（正则表达式）
  name_pattern: "^[a-zA-Z0-9_-]{1,32}$"
  # 是否允许服务器名称重复（不建议启用）
  allow_duplicate_names: false

# 监控和日志配置
monitoring:
  # 是否启用详细日志
  verbose_logging: false
  # 是否记录语音数据统计
  log_voice_stats: true
  # 统计记录间隔（毫秒）
  stats_interval: 300000  # 5分钟
  # 是否启用性能监控
  performance_monitoring: true
