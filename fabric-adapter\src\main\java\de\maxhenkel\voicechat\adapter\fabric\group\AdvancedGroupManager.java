package de.maxhenkel.voicechat.adapter.fabric.group;

import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 高级群组管理器 - Fabric 版本
 */
public class AdvancedGroupManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AdvancedGroupManager.class);
    
    private final VoiceServerClient voiceServerClient;
    
    public AdvancedGroupManager(VoiceServerClient voiceServerClient) {
        this.voiceServerClient = voiceServerClient;
        
        LOGGER.info("Advanced group manager initialized");
    }
}
