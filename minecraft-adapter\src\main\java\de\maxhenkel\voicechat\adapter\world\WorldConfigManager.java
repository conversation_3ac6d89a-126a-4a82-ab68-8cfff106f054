package de.maxhenkel.voicechat.adapter.world;

import de.maxhenkel.voicechat.adapter.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 世界配置管理器
 * 负责管理多世界语音配置和同步
 */
public class WorldConfigManager implements Listener {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WorldConfigManager.class);
    
    private final VoiceServerClient voiceServerClient;
    private final AdapterConfig config;
    
    // 世界配置缓存
    private final Map<String, WorldConfig> worldConfigs = new ConcurrentHashMap<>();
    
    public WorldConfigManager(VoiceServerClient voiceServerClient, AdapterConfig config) {
        this.voiceServerClient = voiceServerClient;
        this.config = config;
        
        initializeDefaultWorldConfigs();
    }
    
    /**
     * 世界配置类
     */
    public static class WorldConfig {
        private boolean voiceEnabled = true;
        private double maxVoiceDistance = 48.0;
        private boolean groupsEnabled = true;
        private boolean crossWorldAllowed = false;
        
        // Getters and Setters
        public boolean isVoiceEnabled() { return voiceEnabled; }
        public void setVoiceEnabled(boolean voiceEnabled) { this.voiceEnabled = voiceEnabled; }
        public double getMaxVoiceDistance() { return maxVoiceDistance; }
        public void setMaxVoiceDistance(double maxVoiceDistance) { this.maxVoiceDistance = maxVoiceDistance; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public void setGroupsEnabled(boolean groupsEnabled) { this.groupsEnabled = groupsEnabled; }
        public boolean isCrossWorldAllowed() { return crossWorldAllowed; }
        public void setCrossWorldAllowed(boolean crossWorldAllowed) { this.crossWorldAllowed = crossWorldAllowed; }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("voiceEnabled", voiceEnabled);
            map.put("maxVoiceDistance", maxVoiceDistance);
            map.put("groupsEnabled", groupsEnabled);
            map.put("crossWorldAllowed", crossWorldAllowed);
            return map;
        }
        
        public static WorldConfig fromMap(Map<String, Object> map) {
            WorldConfig config = new WorldConfig();
            if (map.containsKey("voiceEnabled")) {
                config.setVoiceEnabled((Boolean) map.get("voiceEnabled"));
            }
            if (map.containsKey("maxVoiceDistance")) {
                Number distance = (Number) map.get("maxVoiceDistance");
                config.setMaxVoiceDistance(distance.doubleValue());
            }
            if (map.containsKey("groupsEnabled")) {
                config.setGroupsEnabled((Boolean) map.get("groupsEnabled"));
            }
            if (map.containsKey("crossWorldAllowed")) {
                config.setCrossWorldAllowed((Boolean) map.get("crossWorldAllowed"));
            }
            return config;
        }
    }
    
    /**
     * 初始化默认世界配置
     */
    private void initializeDefaultWorldConfigs() {
        // 从配置文件加载世界配置
        AdapterConfig.WorldsConfig worldsConfig = config.getWorlds();

        // 加载特定世界配置
        for (Map.Entry<String, AdapterConfig.WorldConfig> entry : worldsConfig.getWorldConfigs().entrySet()) {
            String worldName = entry.getKey();
            AdapterConfig.WorldConfig adapterWorldConfig = entry.getValue();

            WorldConfig worldConfig = new WorldConfig();
            worldConfig.setVoiceEnabled(adapterWorldConfig.isVoiceEnabled());
            worldConfig.setMaxVoiceDistance(adapterWorldConfig.getMaxVoiceDistance());
            worldConfig.setGroupsEnabled(adapterWorldConfig.isGroupsEnabled());
            worldConfig.setCrossWorldAllowed(adapterWorldConfig.isCrossWorldAllowed());

            worldConfigs.put(worldName, worldConfig);
            LOGGER.debug("Loaded world config for '{}': voice={}, distance={}, groups={}, crossWorld={}",
                        worldName, worldConfig.isVoiceEnabled(), worldConfig.getMaxVoiceDistance(),
                        worldConfig.isGroupsEnabled(), worldConfig.isCrossWorldAllowed());
        }

        LOGGER.info("Initialized world configurations from config file: {} worlds", worldConfigs.size());

        // 异步同步配置到独立语音服务器
        syncAllConfigsToVoiceServer();
    }
    
    /**
     * 获取世界配置
     */
    public WorldConfig getWorldConfig(String worldName) {
        return worldConfigs.computeIfAbsent(worldName, k -> {
            // 使用配置文件中的默认配置
            AdapterConfig.WorldConfig defaultAdapterConfig = config.getWorlds().getDefaultConfig();

            WorldConfig defaultWorldConfig = new WorldConfig();
            defaultWorldConfig.setVoiceEnabled(defaultAdapterConfig.isVoiceEnabled());
            defaultWorldConfig.setMaxVoiceDistance(defaultAdapterConfig.getMaxVoiceDistance());
            defaultWorldConfig.setGroupsEnabled(defaultAdapterConfig.isGroupsEnabled());
            defaultWorldConfig.setCrossWorldAllowed(defaultAdapterConfig.isCrossWorldAllowed());

            LOGGER.debug("Created default world config for '{}': voice={}, distance={}, groups={}, crossWorld={}",
                        worldName, defaultWorldConfig.isVoiceEnabled(), defaultWorldConfig.getMaxVoiceDistance(),
                        defaultWorldConfig.isGroupsEnabled(), defaultWorldConfig.isCrossWorldAllowed());

            return defaultWorldConfig;
        });
    }
    
    /**
     * 设置世界配置
     */
    public void setWorldConfig(String worldName, WorldConfig config) {
        worldConfigs.put(worldName, config);
        
        // 同步到独立服务器
        syncWorldConfigToVoiceServer(worldName, config);
    }
    
    /**
     * 检查世界是否启用语音
     */
    public boolean isVoiceEnabled(String worldName) {
        WorldConfig config = getWorldConfig(worldName);
        return config.isVoiceEnabled();
    }
    
    /**
     * 检查世界是否启用群组
     */
    public boolean isGroupsEnabled(String worldName) {
        WorldConfig config = getWorldConfig(worldName);
        return config.isGroupsEnabled();
    }
    
    /**
     * 获取世界的最大语音距离
     */
    public double getMaxVoiceDistance(String worldName) {
        WorldConfig config = getWorldConfig(worldName);
        return config.getMaxVoiceDistance();
    }
    
    /**
     * 检查是否允许跨世界语音
     */
    public boolean isCrossWorldAllowed(String worldName) {
        WorldConfig config = getWorldConfig(worldName);
        return config.isCrossWorldAllowed();
    }
    
    /**
     * 添加跨世界规则
     */
    public void addCrossWorldRule(String fromWorld, String toWorld) {
        try {
            voiceServerClient.addCrossWorldRule(fromWorld, toWorld);
            LOGGER.info("Added cross-world rule: {} -> {}", fromWorld, toWorld);
        } catch (Exception e) {
            LOGGER.error("Failed to add cross-world rule: {} -> {}", fromWorld, toWorld, e);
        }
    }
    
    /**
     * 移除跨世界规则
     */
    public void removeCrossWorldRule(String fromWorld, String toWorld) {
        try {
            voiceServerClient.removeCrossWorldRule(fromWorld, toWorld);
            LOGGER.info("Removed cross-world rule: {} -> {}", fromWorld, toWorld);
        } catch (Exception e) {
            LOGGER.error("Failed to remove cross-world rule: {} -> {}", fromWorld, toWorld, e);
        }
    }
    
    /**
     * 玩家切换世界事件处理
     */
    @EventHandler
    public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        World fromWorld = event.getFrom();
        World toWorld = player.getWorld();
        
        String fromWorldName = fromWorld.getName();
        String toWorldName = toWorld.getName();
        
        LOGGER.debug("Player {} changed world from {} to {}", player.getName(), fromWorldName, toWorldName);
        
        // 检查新世界是否启用语音
        if (!isVoiceEnabled(toWorldName)) {
            player.sendMessage("§c该世界已禁用语音聊天");
            return;
        }
        
        // 检查是否需要更新玩家的世界信息
        // 这里可以触发玩家状态更新到独立服务器
        updatePlayerWorldInfo(player, toWorldName);
        
        // 如果不允许跨世界语音，可能需要断开当前的群组连接
        if (!isCrossWorldAllowed(fromWorldName) && !isCrossWorldAllowed(toWorldName)) {
            // 这里可以实现断开跨世界群组的逻辑
            handleCrossWorldGroupDisconnection(player, fromWorldName, toWorldName);
        }
    }
    
    /**
     * 更新玩家世界信息
     */
    private void updatePlayerWorldInfo(Player player, String worldName) {
        // 这里可以实现更新玩家世界信息到独立服务器的逻辑
        // 例如通过PlayerStateManager更新玩家状态
        LOGGER.debug("Updated world info for player {} to world {}", player.getName(), worldName);
    }
    
    /**
     * 处理跨世界群组断开
     */
    private void handleCrossWorldGroupDisconnection(Player player, String fromWorld, String toWorld) {
        // 如果玩家在群组中且不允许跨世界，可以选择：
        // 1. 让玩家离开群组
        // 2. 只是暂停语音传输
        // 3. 发送警告消息
        
        if (!player.hasPermission("voicechat.cross_world")) {
            player.sendMessage("§e由于世界切换，你可能无法与其他世界的群组成员通话");
        }
        
        LOGGER.debug("Handled cross-world group disconnection for player {} ({} -> {})", 
                    player.getName(), fromWorld, toWorld);
    }
    
    /**
     * 同步所有世界配置到独立服务器
     */
    public void syncAllWorldConfigs() {
        for (Map.Entry<String, WorldConfig> entry : worldConfigs.entrySet()) {
            try {
                voiceServerClient.updateWorldConfig(entry.getKey(), entry.getValue().toMap());
            } catch (Exception e) {
                LOGGER.error("Failed to sync world config for {}", entry.getKey(), e);
            }
        }
        LOGGER.info("Synced {} world configurations to server", worldConfigs.size());
    }
    
    /**
     * 获取所有世界配置
     */
    public Map<String, WorldConfig> getAllWorldConfigs() {
        return new HashMap<>(worldConfigs);
    }
    
    /**
     * 重新加载世界配置
     */
    public void reloadWorldConfigs() {
        worldConfigs.clear();
        initializeDefaultWorldConfigs();
        
        // 从独立服务器重新加载配置
        for (World world : org.bukkit.Bukkit.getWorlds()) {
            getWorldConfig(world.getName()); // 这会触发从服务器加载
        }
        
        LOGGER.info("Reloaded world configurations");
    }

    /**
     * 同步所有世界配置到独立语音服务器
     */
    private void syncAllConfigsToVoiceServer() {
        if (worldConfigs.isEmpty()) {
            return;
        }

        // 异步执行同步
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Map<String, Map<String, Object>> configsToSync = new HashMap<>();
                    for (Map.Entry<String, WorldConfig> entry : worldConfigs.entrySet()) {
                        configsToSync.put(entry.getKey(), entry.getValue().toMap());
                    }

                    boolean success = voiceServerClient.syncWorldConfigs(configsToSync);
                    if (success) {
                        LOGGER.info("Successfully synced {} world configs to voice server", configsToSync.size());
                    } else {
                        LOGGER.warn("Failed to sync world configs to voice server");
                    }
                } catch (Exception e) {
                    LOGGER.error("Error syncing world configs to voice server", e);
                }
            }
        }.runTaskAsynchronously(org.bukkit.Bukkit.getPluginManager().getPlugin("VoiceChatAdapter"));
    }

    /**
     * 同步单个世界配置到独立语音服务器
     */
    private void syncWorldConfigToVoiceServer(String worldName, WorldConfig config) {
        // 异步执行同步
        new org.bukkit.scheduler.BukkitRunnable() {
            @Override
            public void run() {
                try {
                    boolean success = voiceServerClient.syncWorldConfig(worldName, config.toMap());
                    if (success) {
                        LOGGER.debug("Successfully synced world config for {} to voice server", worldName);
                    } else {
                        LOGGER.warn("Failed to sync world config for {} to voice server", worldName);
                    }
                } catch (Exception e) {
                    LOGGER.error("Error syncing world config for {} to voice server", worldName, e);
                }
            }
        }.runTaskAsynchronously(org.bukkit.Bukkit.getPluginManager().getPlugin("VoiceChatAdapter"));
    }
}
