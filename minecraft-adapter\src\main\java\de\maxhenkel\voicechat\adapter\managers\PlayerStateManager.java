package de.maxhenkel.voicechat.adapter.managers;

import de.maxhenkel.voicechat.adapter.VoiceChatAdapterPlugin;
import de.maxhenkel.voicechat.adapter.model.PlayerState;
import de.maxhenkel.voicechat.adapter.network.PlayerStateNetworkManager;
import de.maxhenkel.voicechat.adapter.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.util.FriendlyByteBuf;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家状态管理器 - 管理所有玩家的语音聊天状态
 */
public class PlayerStateManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerStateManager.class);
    
    private final VoiceChatAdapterPlugin plugin;
    private final ConcurrentHashMap<UUID, PlayerState> states;
    private final ConcurrentHashMap<UUID, Long> disconnectedPlayerTimestamps; // 记录玩家断开连接的时间
    private final PlayerStateNetworkManager networkManager;
    private final VoiceServerClient voiceServerClient;
    private BukkitRunnable connectionSyncTask;
    private BukkitRunnable cleanupTask;

    public PlayerStateManager(VoiceChatAdapterPlugin plugin) {
        this.plugin = plugin;
        this.states = new ConcurrentHashMap<>();
        this.disconnectedPlayerTimestamps = new ConcurrentHashMap<>();
        this.networkManager = new PlayerStateNetworkManager(plugin);
        this.voiceServerClient = plugin.getVoiceServerClient();
    }

    /**
     * 初始化网络管理器
     */
    public void initialize() {
        networkManager.register();
        startConnectionSyncTask();
        startCleanupTask();
        LOGGER.info("PlayerStateManager initialized with network support");
    }

    /**
     * 关闭网络管理器
     */
    public void shutdown() {
        stopConnectionSyncTask();
        stopCleanupTask();
        networkManager.unregister();
        LOGGER.info("PlayerStateManager shutdown");
    }

    /**
     * 启动连接状态同步任务
     */
    private void startConnectionSyncTask() {
        connectionSyncTask = new BukkitRunnable() {
            @Override
            public void run() {
                syncConnectionStates();
            }
        };
        // 每5秒同步一次连接状态
        connectionSyncTask.runTaskTimerAsynchronously(plugin, 100L, 100L); // 5秒后开始，每5秒执行一次
    }

    /**
     * 停止连接状态同步任务
     */
    private void stopConnectionSyncTask() {
        if (connectionSyncTask != null) {
            connectionSyncTask.cancel();
            connectionSyncTask = null;
        }
    }

    /**
     * 启动断开连接玩家清理任务
     */
    private void startCleanupTask() {
        cleanupTask = new BukkitRunnable() {
            @Override
            public void run() {
                cleanupDisconnectedPlayers();
            }
        };
        // 每30秒执行一次清理任务
        cleanupTask.runTaskTimerAsynchronously(plugin, 600L, 600L); // 30秒后开始，每30秒执行一次
    }

    /**
     * 停止断开连接玩家清理任务
     */
    private void stopCleanupTask() {
        if (cleanupTask != null) {
            cleanupTask.cancel();
            cleanupTask = null;
        }
    }

    /**
     * 清理长时间断开连接的玩家状态
     */
    private void cleanupDisconnectedPlayers() {
        try {
            long currentTime = System.currentTimeMillis();
            long cleanupThreshold = 60000; // 60秒后清理断开连接的玩家

            Set<UUID> playersToRemove = new HashSet<>();

            for (Map.Entry<UUID, PlayerState> entry : states.entrySet()) {
                UUID playerUuid = entry.getKey();
                PlayerState state = entry.getValue();

                // 检查是否是本地玩家
                Player localPlayer = plugin.getServer().getPlayer(playerUuid);
                if (localPlayer != null && localPlayer.isOnline()) {
                    // 这是本地玩家，不要清理
                    continue;
                }

                // 检查是否是长时间断开连接的玩家
                if (state.isDisconnected()) {
                    Long disconnectedTime = disconnectedPlayerTimestamps.get(playerUuid);
                    if (disconnectedTime == null) {
                        // 记录断开连接时间
                        disconnectedPlayerTimestamps.put(playerUuid, currentTime);
                    } else if (currentTime - disconnectedTime > cleanupThreshold) {
                        // 超过阈值时间，标记为移除
                        playersToRemove.add(playerUuid);
                    }
                } else {
                    // 玩家重新连接，移除断开连接时间记录
                    disconnectedPlayerTimestamps.remove(playerUuid);
                }
            }

            // 移除长时间断开连接的玩家
            for (UUID playerUuid : playersToRemove) {
                PlayerState removedState = states.remove(playerUuid);
                disconnectedPlayerTimestamps.remove(playerUuid);

                if (removedState != null) {
                    LOGGER.debug("Cleaned up long-disconnected player state: {}", removedState.getName());

                    // 通知所有客户端移除该玩家
                    for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                        networkManager.sendPlayerState(onlinePlayer,
                            new PlayerState(playerUuid, removedState.getName(), false, true, null));
                    }
                }
            }

            if (!playersToRemove.isEmpty()) {
                LOGGER.debug("Cleaned up {} long-disconnected player states", playersToRemove.size());
            }

        } catch (Exception e) {
            LOGGER.warn("Failed to cleanup disconnected players: {}", e.getMessage());
        }
    }

    /**
     * 同步连接状态
     */
    private void syncConnectionStates() {
        try {
            VoiceServerClient voiceServerClient = plugin.getVoiceServerClient();
            if (voiceServerClient == null || !voiceServerClient.isConnected()) {
                return;
            }

            // 同步跨服务器玩家状态
            syncCrossServerPlayerStates();

            Map<UUID, Boolean> connectionStates = voiceServerClient.getPlayerConnectionStates();
            boolean hasChanges = false;

            for (Map.Entry<UUID, Boolean> entry : connectionStates.entrySet()) {
                UUID playerUuid = entry.getKey();
                boolean isConnected = entry.getValue();

                PlayerState state = states.get(playerUuid);
                if (state != null) {
                    boolean wasDisconnected = state.isDisconnected();
                    boolean shouldBeDisconnected = !isConnected;

                    if (wasDisconnected != shouldBeDisconnected) {
                        state.setDisconnected(shouldBeDisconnected);
                        broadcastState(state);
                        hasChanges = true;
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.warn("Failed to sync connection states: {}", e.getMessage());
        }
    }
    
    /**
     * 玩家加入服务器时的默认状态
     */
    public void onPlayerJoin(Player player) {
        PlayerState state = createDefaultState(player);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Setting default state for {}: {}", player.getName(), state);
    }
    
    /**
     * 玩家离开服务器
     */
    public void onPlayerQuit(Player player) {
        PlayerState state = new PlayerState(player.getUniqueId(), player.getName(), false, true, null);
        states.remove(player.getUniqueId());
        broadcastState(state);
        LOGGER.debug("Removing state for {}", player.getName());
    }
    
    /**
     * 玩家连接到语音聊天
     */
    public void onPlayerVoicechatConnect(Player player) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
        }

        state.setDisconnected(false);
        // 清除断开连接的时间记录
        disconnectedPlayerTimestamps.remove(player.getUniqueId());
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Set {} to voice connected: {}", player.getName(), state);
    }
    
    /**
     * 玩家断开语音聊天连接
     */
    public void onPlayerVoicechatDisconnect(UUID playerUuid) {
        PlayerState state = states.get(playerUuid);
        if (state == null) {
            return;
        }

        state.setDisconnected(true);
        // 记录断开连接的时间
        disconnectedPlayerTimestamps.put(playerUuid, System.currentTimeMillis());
        broadcastState(state);
        LOGGER.debug("Set {} to voice disconnected: {}", playerUuid, state);
    }
    
    /**
     * 设置玩家的群组
     */
    public void setGroup(Player player, UUID groupId) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
            LOGGER.debug("Creating default state for {}: {}", player.getName(), state);
        }

        state.setGroup(groupId);
        states.put(player.getUniqueId(), state);

        // 立即广播状态更新给所有玩家
        broadcastState(state);

        // 同时向所有玩家发送完整的PlayerStates更新
        if (networkManager != null) {
            for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                networkManager.sendAllPlayerStates(onlinePlayer, states);
            }
        }

        LOGGER.debug("Setting group for {}: groupId={}", player.getName(), groupId);
    }



    /**
     * 更新玩家状态（禁用/启用语音）
     */
    public void updatePlayerState(Player player, boolean disabled) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            state = createDefaultState(player);
        }
        
        state.setDisabled(disabled);
        states.put(player.getUniqueId(), state);
        broadcastState(state);
        LOGGER.debug("Updated state for {}: disabled={}", player.getName(), disabled);
    }
    
    /**
     * 获取玩家状态
     */
    public PlayerState getState(UUID playerUuid) {
        return states.get(playerUuid);
    }

    /**
     * 获取玩家的群组ID
     */
    public UUID getGroup(Player player) {
        PlayerState state = states.get(player.getUniqueId());
        if (state == null) {
            return null;
        }
        return state.getGroup();
    }
    
    /**
     * 获取所有玩家状态
     */
    public Collection<PlayerState> getStates() {
        return states.values();
    }
    
    /**
     * 向新加入的玩家发送所有玩家状态
     */
    public void sendAllStatesToPlayer(Player player) {
        try {
            // 检查是否有状态需要发送
            if (states.isEmpty()) {
                LOGGER.debug("No player states to send to {}", player.getName());
                return;
            }

            // 使用网络管理器发送状态
            networkManager.sendAllPlayerStates(player, states);
            LOGGER.debug("Sent {} player states to {}", states.size(), player.getName());

        } catch (Exception e) {
            LOGGER.error("Failed to send player states to {}: {}", player.getName(), e.getMessage());
        }
    }
    
    /**
     * 广播玩家状态更新给所有在线玩家
     */
    private void broadcastState(PlayerState state) {
        try {
            // 使用网络管理器广播状态
            networkManager.broadcastPlayerState(state);
            LOGGER.debug("Broadcasted state update: {}", state);
        } catch (Exception e) {
            LOGGER.error("Failed to broadcast player state: {}", e.getMessage());
        }
    }
    
    /**
     * 创建默认玩家状态
     */
    private PlayerState createDefaultState(Player player) {
        // 如果玩家在线，则设置为connected状态
        boolean isDisconnected = !player.isOnline();
        return new PlayerState(player.getUniqueId(), player.getName(), false, isDisconnected);
    }

    /**
     * 同步跨服务器玩家状态
     */
    private void syncCrossServerPlayerStates() {
        try {
            VoiceServerClient voiceServerClient = plugin.getVoiceServerClient();
            if (voiceServerClient == null || !voiceServerClient.isConnected()) {
                return;
            }

            // 获取所有服务器的在线玩家信息
            java.util.List<Map<String, Object>> allPlayers = voiceServerClient.getAllOnlinePlayers();
            if (allPlayers == null || allPlayers.isEmpty()) {
                return;
            }

            boolean hasChanges = false;
            String currentServerName = plugin.getAdapterConfig().getServerName();

            // 记录当前同步的跨服务器玩家，用于清理不再在线的玩家
            Set<UUID> currentCrossServerPlayers = new HashSet<>();

            for (Map<String, Object> playerInfo : allPlayers) {
                try {
                    String uuidStr = (String) playerInfo.get("uuid");
                    String playerName = (String) playerInfo.get("name");
                    String serverName = (String) playerInfo.get("serverName");
                    Boolean isConnected = (Boolean) playerInfo.get("connected");
                    String groupIdStr = (String) playerInfo.get("groupId");

                    if (uuidStr == null || playerName == null) {
                        continue;
                    }

                    UUID playerUuid = UUID.fromString(uuidStr);

                    // 跳过本地服务器的玩家（它们已经通过本地事件处理）
                    if (currentServerName.equals(serverName)) {
                        continue;
                    }

                    // 记录跨服务器玩家
                    currentCrossServerPlayers.add(playerUuid);

                    // 获取或创建玩家状态
                    PlayerState state = states.get(playerUuid);
                    if (state == null) {
                        // 创建跨服务器玩家的状态
                        state = new PlayerState(playerUuid, playerName, false, !Boolean.TRUE.equals(isConnected));
                        states.put(playerUuid, state);
                        hasChanges = true;
                        LOGGER.debug("Added cross-server player state: {} from server {}", playerName, serverName);
                    } else {
                        // 检查是否是本地玩家（避免覆盖本地玩家状态）
                        Player localPlayer = plugin.getServer().getPlayer(playerUuid);
                        if (localPlayer != null && localPlayer.isOnline()) {
                            // 这是本地玩家，但如果服务器名称不匹配，说明可能是重复状态
                            if (!currentServerName.equals(serverName)) {
                                LOGGER.debug("Detected duplicate state for local player {} from server {}, ignoring",
                                           playerName, serverName);
                                continue;
                            }
                        }

                        // 更新现有状态
                        boolean wasDisconnected = state.isDisconnected();
                        boolean shouldBeDisconnected = !Boolean.TRUE.equals(isConnected);

                        if (wasDisconnected != shouldBeDisconnected) {
                            state.setDisconnected(shouldBeDisconnected);
                            hasChanges = true;
                        }

                        // 更新玩家名称（可能会变化）
                        if (!playerName.equals(state.getName())) {
                            state.setName(playerName);
                            hasChanges = true;
                        }
                    }

                    // 更新群组信息
                    UUID currentGroupId = state.getGroup();
                    UUID newGroupId = groupIdStr != null ? UUID.fromString(groupIdStr) : null;

                    if (!java.util.Objects.equals(currentGroupId, newGroupId)) {
                        state.setGroup(newGroupId);
                        hasChanges = true;
                        LOGGER.debug("Updated group for cross-server player {}: {} -> {}",
                                   playerName, currentGroupId, newGroupId);
                    }

                } catch (Exception e) {
                    LOGGER.warn("Failed to process cross-server player info: {}", e.getMessage());
                }
            }

            // 清理不再在线的跨服务器玩家状态
            Set<UUID> playersToRemove = new HashSet<>();
            for (Map.Entry<UUID, PlayerState> entry : states.entrySet()) {
                UUID playerUuid = entry.getKey();
                PlayerState state = entry.getValue();

                // 检查是否是本地玩家
                Player localPlayer = plugin.getServer().getPlayer(playerUuid);
                if (localPlayer != null && localPlayer.isOnline()) {
                    // 这是本地玩家，不要移除
                    continue;
                }

                // 检查是否是当前在线的跨服务器玩家
                if (!currentCrossServerPlayers.contains(playerUuid)) {
                    // 这个玩家不再在任何服务器上在线，标记为移除
                    playersToRemove.add(playerUuid);
                }
            }

            // 移除不再在线的跨服务器玩家
            for (UUID playerUuid : playersToRemove) {
                PlayerState removedState = states.remove(playerUuid);
                if (removedState != null) {
                    // 发送一个 disconnected=true 的状态来通知客户端移除该玩家
                    PlayerState disconnectedState = new PlayerState(playerUuid, removedState.getName(), false, true, null);
                    broadcastState(disconnectedState);
                    hasChanges = true;
                    LOGGER.debug("Removed offline cross-server player state: {}", removedState.getName());
                }
            }

            // 如果有变化，广播给所有本地玩家
            if (hasChanges || !playersToRemove.isEmpty()) {
                // 先发送移除的玩家状态（确保客户端收到断开连接的信号）
                for (UUID removedPlayerUuid : playersToRemove) {
                    PlayerState disconnectedState = new PlayerState(removedPlayerUuid, "Unknown", false, true, null);
                    for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                        networkManager.sendPlayerState(onlinePlayer, disconnectedState);
                    }
                }

                // 然后发送完整的状态列表
                for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                    networkManager.sendAllPlayerStates(onlinePlayer, states);
                }
                LOGGER.debug("Synced cross-server player states, total states: {}, removed: {}",
                           states.size(), playersToRemove.size());
            }

        } catch (Exception e) {
            LOGGER.warn("Failed to sync cross-server player states: {}", e.getMessage());
        }
    }
}
