package de.maxhenkel.voicechat;

import de.maxhenkel.voicechat.integration.clothconfig.ClothConfig;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents;
import net.fabricmc.fabric.api.networking.v1.PayloadTypeRegistry;
import net.minecraft.resources.ResourceLocation;

@Environment(EnvType.CLIENT)
public class FabricVoicechatClientMod extends VoicechatClient implements ClientModInitializer {

    @Override
    public void onInitializeClient() {
        initializeClient();

        ClothConfig.init();

        // 注册插件消息通道支持
        registerPluginChannels();
    }

    /**
     * 注册插件消息通道以支持与服务器的兼容性
     */
    private void registerPluginChannels() {
        // 在连接到服务器时注册插件消息通道
        ClientPlayConnectionEvents.JOIN.register((handler, sender, client) -> {
            // 注册语音聊天相关的插件消息通道
            try {
                // 这些通道需要在客户端注册以便服务器知道客户端支持它们
                ResourceLocation playerStateChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "player_state");
                ResourceLocation playerStatesChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "player_states");
                ResourceLocation secretChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "secret");
                ResourceLocation addGroupChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "add_group");
                ResourceLocation removeGroupChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "remove_group");
                ResourceLocation joinedGroupChannel = ResourceLocation.fromNamespaceAndPath("voicechat", "joined_group");

                Voicechat.LOGGER.info("Registered plugin message channels for voice chat compatibility");
            } catch (Exception e) {
                Voicechat.LOGGER.warn("Failed to register plugin message channels: {}", e.getMessage());
            }
        });
    }

}
