package de.maxhenkel.voicechat.adapter.fabric.config;

import org.yaml.snakeyaml.Yaml;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * 适配器配置类 - Fabric 版本
 */
public class AdapterConfig {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AdapterConfig.class);
    
    private final VoiceServerConfig voiceServer;
    private final SyncConfig sync;
    private final String serverName;
    private final CrossServerConfig crossServer;
    private final CrossWorldConfig crossWorld;
    private final PermissionConfig permissions;
    private final WorldsConfig worlds;
    
    public AdapterConfig(VoiceServerConfig voiceServer, SyncConfig sync, String serverName,
                        CrossServerConfig crossServer, CrossWorldConfig crossWorld, PermissionConfig permissions,
                        WorldsConfig worlds) {
        this.voiceServer = voiceServer;
        this.sync = sync;
        this.serverName = serverName;
        this.crossServer = crossServer;
        this.crossWorld = crossWorld;
        this.permissions = permissions;
        this.worlds = worlds;
    }
    
    /**
     * 语音服务器配置
     */
    public static class VoiceServerConfig {
        private final String host;
        private final int port;
        private final String apiEndpoint;
        private final String authToken;
        
        public VoiceServerConfig(String host, int port, String apiEndpoint, String authToken) {
            this.host = host;
            this.port = port;
            this.apiEndpoint = apiEndpoint;
            this.authToken = authToken;
        }
        
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getApiEndpoint() { return apiEndpoint; }
        public String getAuthToken() { return authToken; }
    }
    
    /**
     * 同步配置
     */
    public static class SyncConfig {
        private final int positionSyncInterval;
        private final int permissionSyncInterval;
        private final boolean syncOnMove;
        private final double minMoveDistance;
        
        public SyncConfig(int positionSyncInterval, int permissionSyncInterval, 
                         boolean syncOnMove, double minMoveDistance) {
            this.positionSyncInterval = positionSyncInterval;
            this.permissionSyncInterval = permissionSyncInterval;
            this.syncOnMove = syncOnMove;
            this.minMoveDistance = minMoveDistance;
        }
        
        public int getPositionSyncInterval() { return positionSyncInterval; }
        public int getPermissionSyncInterval() { return permissionSyncInterval; }
        public boolean isSyncOnMove() { return syncOnMove; }
        public double getMinMoveDistance() { return minMoveDistance; }
    }
    
    /**
     * 从文件加载配置
     */
    public static AdapterConfig load(File file) throws Exception {
        // 如果配置文件不存在，创建默认配置
        if (!file.exists()) {
            createDefaultConfig(file);
        }
        
        Yaml yaml = new Yaml();
        Map<String, Object> data;
        
        try (FileInputStream fis = new FileInputStream(file)) {
            data = yaml.load(fis);
        }
        
        if (data == null) {
            throw new Exception("Failed to load configuration from " + file.getAbsolutePath());
        }
        
        // 语音服务器配置
        Map<String, Object> voiceServerData = getMap(data, "voice-server");
        String host = getString(voiceServerData, "host", "localhost");
        int port = getInt(voiceServerData, "port", 24454);
        String apiEndpoint = getString(voiceServerData, "api-endpoint", "http://localhost:8080");
        String authToken = getString(voiceServerData, "auth-token", "change-this-secret-token");
        
        VoiceServerConfig voiceServerConfig = new VoiceServerConfig(host, port, apiEndpoint, authToken);
        
        // 同步配置
        Map<String, Object> syncData = getMap(data, "sync");
        int positionSyncInterval = getInt(syncData, "position-interval", 1000);
        int permissionSyncInterval = getInt(syncData, "permission-interval", 5000);
        boolean syncOnMove = getBoolean(syncData, "sync-on-move", true);
        double minMoveDistance = getDouble(syncData, "min-move-distance", 1.0);
        
        SyncConfig syncConfig = new SyncConfig(positionSyncInterval, permissionSyncInterval, 
                                              syncOnMove, minMoveDistance);
        
        // 服务器名称
        String serverName = getString(data, "server-name", "default");

        // 跨服务器配置
        Map<String, Object> crossServerData = getMap(data, "cross-server");
        boolean crossServerEnabled = getBoolean(crossServerData, "enabled", true);
        List<String> allowedServers = getStringList(crossServerData, "allowed-servers");
        CrossServerConfig crossServerConfig = new CrossServerConfig(crossServerEnabled, allowedServers);

        // 跨世界配置
        Map<String, Object> crossWorldData = getMap(data, "cross-world");
        boolean crossWorldEnabled = getBoolean(crossWorldData, "enabled", true);
        List<String> allowedWorlds = getStringList(crossWorldData, "allowed-worlds");
        CrossWorldConfig crossWorldConfig = new CrossWorldConfig(crossWorldEnabled, allowedWorlds);

        // 权限配置
        PermissionConfig permissionConfig = loadPermissionConfig(data);

        // 世界配置
        WorldsConfig worldsConfig = loadWorldsConfig(data);

        return new AdapterConfig(voiceServerConfig, syncConfig, serverName,
                               crossServerConfig, crossWorldConfig, permissionConfig, worldsConfig);
    }
    
    /**
     * 创建默认配置文件
     */
    private static void createDefaultConfig(File file) throws IOException {
        file.getParentFile().mkdirs();
        
        try (InputStream is = AdapterConfig.class.getResourceAsStream("/config.yml");
             FileOutputStream fos = new FileOutputStream(file)) {
            
            if (is != null) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = is.read(buffer)) > 0) {
                    fos.write(buffer, 0, length);
                }
                LOGGER.info("Created default configuration file: {}", file.getAbsolutePath());
            } else {
                throw new IOException("Could not find default config.yml in resources");
            }
        }
    }
    
    /**
     * 加载权限配置
     */
    private static PermissionConfig loadPermissionConfig(Map<String, Object> data) {
        Map<String, Object> permissionsData = getMap(data, "permissions");
        Map<String, Object> defaultsData = getMap(permissionsData, "defaults");
        
        boolean listen = getBoolean(defaultsData, "listen", true);
        boolean speak = getBoolean(defaultsData, "speak", true);
        boolean groups = getBoolean(defaultsData, "groups", true);
        boolean createGroup = getBoolean(defaultsData, "create-group", true);
        boolean joinGroup = getBoolean(defaultsData, "join-group", true);
        boolean crossWorld = getBoolean(defaultsData, "cross-world", true);
        boolean crossServer = getBoolean(defaultsData, "cross-server", true);
        boolean record = getBoolean(defaultsData, "record", false);
        boolean admin = getBoolean(defaultsData, "admin", false);

        return new PermissionConfig(listen, speak, groups, createGroup, joinGroup,
                                  crossWorld, crossServer, record, admin);
    }

    /**
     * 加载世界配置
     */
    private static WorldsConfig loadWorldsConfig(Map<String, Object> data) {
        Map<String, Object> worldsData = getMap(data, "worlds");

        // 默认世界配置
        Map<String, Object> defaultData = getMap(worldsData, "default");
        double defaultMaxDistance = getDouble(defaultData, "max-voice-distance", 48.0);
        boolean defaultVoiceEnabled = getBoolean(defaultData, "voice-enabled", true);
        boolean defaultGroupsEnabled = getBoolean(defaultData, "groups-enabled", true);
        boolean defaultCrossWorldAllowed = getBoolean(defaultData, "cross-world-allowed", false);

        WorldConfig defaultConfig = new WorldConfig(defaultVoiceEnabled, defaultMaxDistance,
                                                   defaultGroupsEnabled, defaultCrossWorldAllowed);

        // 特定世界配置
        Map<String, WorldConfig> worldConfigs = new HashMap<>();
        Map<String, Object> specificData = getMap(worldsData, "specific");

        for (Map.Entry<String, Object> entry : specificData.entrySet()) {
            String worldName = entry.getKey();
            if (entry.getValue() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> worldData = (Map<String, Object>) entry.getValue();

                double maxDistance = getDouble(worldData, "max-voice-distance", defaultMaxDistance);
                boolean voiceEnabled = getBoolean(worldData, "voice-enabled", defaultVoiceEnabled);
                boolean groupsEnabled = getBoolean(worldData, "groups-enabled", defaultGroupsEnabled);
                boolean crossWorldAllowed = getBoolean(worldData, "cross-world-allowed", defaultCrossWorldAllowed);

                WorldConfig worldConfig = new WorldConfig(voiceEnabled, maxDistance, groupsEnabled, crossWorldAllowed);
                worldConfigs.put(worldName, worldConfig);
            }
        }

        return new WorldsConfig(defaultConfig, worldConfigs);
    }

    // 辅助方法
    @SuppressWarnings("unchecked")
    private static Map<String, Object> getMap(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return Map.of();
    }
    
    private static String getString(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        return value instanceof String ? (String) value : defaultValue;
    }
    
    private static int getInt(Map<String, Object> data, String key, int defaultValue) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
    
    private static double getDouble(Map<String, Object> data, String key, double defaultValue) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    private static boolean getBoolean(Map<String, Object> data, String key, boolean defaultValue) {
        Object value = data.get(key);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }
    
    @SuppressWarnings("unchecked")
    private static List<String> getStringList(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return List.of();
    }

    // Getters
    public VoiceServerConfig getVoiceServer() { return voiceServer; }
    public SyncConfig getSync() { return sync; }
    public String getServerName() { return serverName; }
    public CrossServerConfig getCrossServer() { return crossServer; }
    public CrossWorldConfig getCrossWorld() { return crossWorld; }
    public PermissionConfig getPermissions() { return permissions; }
    public WorldsConfig getWorlds() { return worlds; }

    /**
     * 跨服务器配置
     */
    public static class CrossServerConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedServers;

        public CrossServerConfig(boolean enabled, java.util.List<String> allowedServers) {
            this.enabled = enabled;
            this.allowedServers = allowedServers != null ? allowedServers : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedServers() { return allowedServers; }
        public boolean isServerAllowed(String serverName) {
            return allowedServers.isEmpty() || allowedServers.contains(serverName);
        }
    }

    /**
     * 跨世界配置
     */
    public static class CrossWorldConfig {
        private final boolean enabled;
        private final java.util.List<String> allowedWorlds;

        public CrossWorldConfig(boolean enabled, java.util.List<String> allowedWorlds) {
            this.enabled = enabled;
            this.allowedWorlds = allowedWorlds != null ? allowedWorlds : new java.util.ArrayList<>();
        }

        public boolean isEnabled() { return enabled; }
        public java.util.List<String> getAllowedWorlds() { return allowedWorlds; }
        public boolean isWorldAllowed(String worldName) {
            return allowedWorlds.isEmpty() || allowedWorlds.contains(worldName);
        }
    }

    /**
     * 权限配置
     */
    public static class PermissionConfig {
        private final boolean listen;
        private final boolean speak;
        private final boolean groups;
        private final boolean createGroup;
        private final boolean joinGroup;
        private final boolean crossWorld;
        private final boolean crossServer;
        private final boolean record;
        private final boolean admin;

        public PermissionConfig(boolean listen, boolean speak, boolean groups, boolean createGroup,
                              boolean joinGroup, boolean crossWorld, boolean crossServer,
                              boolean record, boolean admin) {
            this.listen = listen;
            this.speak = speak;
            this.groups = groups;
            this.createGroup = createGroup;
            this.joinGroup = joinGroup;
            this.crossWorld = crossWorld;
            this.crossServer = crossServer;
            this.record = record;
            this.admin = admin;
        }

        public boolean isListen() { return listen; }
        public boolean isSpeak() { return speak; }
        public boolean isGroups() { return groups; }
        public boolean isCreateGroup() { return createGroup; }
        public boolean isJoinGroup() { return joinGroup; }
        public boolean isCrossWorld() { return crossWorld; }
        public boolean isCrossServer() { return crossServer; }
        public boolean isRecord() { return record; }
        public boolean isAdmin() { return admin; }
    }

    /**
     * 世界配置
     */
    public static class WorldsConfig {
        private final WorldConfig defaultConfig;
        private final java.util.Map<String, WorldConfig> worldConfigs;

        public WorldsConfig(WorldConfig defaultConfig, java.util.Map<String, WorldConfig> worldConfigs) {
            this.defaultConfig = defaultConfig;
            this.worldConfigs = worldConfigs != null ? worldConfigs : new java.util.HashMap<>();
        }

        public WorldConfig getDefaultConfig() { return defaultConfig; }
        public java.util.Map<String, WorldConfig> getWorldConfigs() { return worldConfigs; }

        /**
         * 获取指定世界的配置，如果不存在则返回默认配置
         */
        public WorldConfig getWorldConfig(String worldName) {
            return worldConfigs.getOrDefault(worldName, defaultConfig);
        }
    }

    /**
     * 单个世界的配置
     */
    public static class WorldConfig {
        private final boolean voiceEnabled;
        private final double maxVoiceDistance;
        private final boolean groupsEnabled;
        private final boolean crossWorldAllowed;

        public WorldConfig(boolean voiceEnabled, double maxVoiceDistance,
                          boolean groupsEnabled, boolean crossWorldAllowed) {
            this.voiceEnabled = voiceEnabled;
            this.maxVoiceDistance = maxVoiceDistance;
            this.groupsEnabled = groupsEnabled;
            this.crossWorldAllowed = crossWorldAllowed;
        }

        public boolean isVoiceEnabled() { return voiceEnabled; }
        public double getMaxVoiceDistance() { return maxVoiceDistance; }
        public boolean isGroupsEnabled() { return groupsEnabled; }
        public boolean isCrossWorldAllowed() { return crossWorldAllowed; }
    }
}
