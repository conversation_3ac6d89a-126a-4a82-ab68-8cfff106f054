apply plugin: 'fabric-loom'
apply plugin: 'com.gradleup.shadow'

group = 'de.maxhenkel.voicechat'
version = '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    maven { url = 'https://maven.fabricmc.net/' }
    maven { url = 'https://repo.spongepowered.org/repository/maven-public/' }
    maven { url = 'https://maven.maxhenkel.de/repository/public' }
    maven { url = 'https://repo.papermc.io/repository/maven-public/' }
    maven { url = 'https://maven.terraformersmc.com/releases' }
    maven { url = 'https://repo.viaversion.com/' }
}

dependencies {
    minecraft "com.mojang:minecraft:${minecraft_version}"
    mappings "net.fabricmc:yarn:${yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${fabric_loader_version}"
    
    // Fabric API
    modImplementation "net.fabricmc.fabric-api:fabric-api:${fabric_api_version}"
    
    // 权限 API
    modImplementation "me.lucko:fabric-permissions-api:${fabric_permission_api_version}"
    
    // HTTP Client - 与 minecraft-adapter 完全一致
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    
    // JSON处理 - 与 minecraft-adapter 完全一致
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    
    // 配置文件处理 - 与 minecraft-adapter 完全一致
    implementation 'org.yaml:snakeyaml:2.1'
    
    // 日志 - 与 minecraft-adapter 完全一致
    implementation 'org.slf4j:slf4j-api:2.0.7'
    
    // Shadow 依赖
    shadow 'com.squareup.okhttp3:okhttp:4.11.0'
    shadow 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    shadow 'org.yaml:snakeyaml:2.1'
    
    // 测试 - 与 minecraft-adapter 完全一致
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.0'
    testImplementation 'org.mockito:mockito-core:5.5.0'
}

processResources {
    inputs.property "version", project.version
    
    filesMatching("fabric.mod.json") {
        expand "version": project.version,
               "minecraft_version": minecraft_version,
               "fabric_loader_version": fabric_loader_version,
               "fabric_api_version": fabric_api_version
    }
}

tasks.withType(JavaCompile).configureEach {
    it.options.release = 17
}

jar {
    from("LICENSE") {
        rename { "${it}_${project.archivesBaseName}"}
    }
}

shadowJar {
    configurations = [project.configurations.shadow]
    archiveClassifier = 'shadow'
    
    // 重定位依赖包以避免冲突 - 与 minecraft-adapter 一致
    relocate 'okhttp3', 'de.maxhenkel.voicechat.adapter.shadow.okhttp3'
    relocate 'okio', 'de.maxhenkel.voicechat.adapter.shadow.okio'
    relocate 'com.fasterxml.jackson', 'de.maxhenkel.voicechat.adapter.shadow.jackson'
    relocate 'org.yaml.snakeyaml', 'de.maxhenkel.voicechat.adapter.shadow.snakeyaml'
}

test {
    useJUnitPlatform()
}
