package de.maxhenkel.voicechat.adapter.fabric.managers;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import de.maxhenkel.voicechat.adapter.fabric.model.PlayerState;
import net.minecraft.server.network.ServerPlayerEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家状态管理器 - Fabric 版本
 * 完全对应 minecraft-adapter 的 PlayerStateManager
 */
public class PlayerStateManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerStateManager.class);

    private final FabricVoiceChatAdapter plugin;
    private final ConcurrentHashMap<UUID, PlayerState> playerStates = new ConcurrentHashMap<>();

    public PlayerStateManager(FabricVoiceChatAdapter plugin) {
        this.plugin = plugin;

        LOGGER.info("Player state manager initialized");
    }

    public void initialize() {
        // TODO: 实现网络通信初始化
        LOGGER.info("Player state manager network initialized");
    }

    /**
     * 玩家加入时的处理
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        UUID playerUuid = player.getUuid();
        String playerName = player.getName().getString();

        // 创建玩家状态
        PlayerState playerState = new PlayerState(playerUuid, playerName, false, false);
        playerStates.put(playerUuid, playerState);

        LOGGER.debug("Player {} joined, state registered", playerName);
    }

    /**
     * 玩家退出时的处理
     */
    public void onPlayerQuit(ServerPlayerEntity player) {
        UUID playerUuid = player.getUuid();
        String playerName = player.getName().getString();

        // 移除玩家状态
        playerStates.remove(playerUuid);

        LOGGER.debug("Player {} quit, state removed", playerName);
    }

    /**
     * 发送所有玩家状态给指定玩家
     */
    public void sendAllStatesToPlayer(ServerPlayerEntity player) {
        // TODO: 实现状态同步
        LOGGER.debug("Sending all player states to {}", player.getName().getString());
    }

    /**
     * 获取玩家状态
     */
    public PlayerState getPlayerState(UUID playerUuid) {
        return playerStates.get(playerUuid);
    }

    /**
     * 更新玩家状态
     */
    public void updatePlayerState(UUID playerUuid, PlayerState newState) {
        playerStates.put(playerUuid, newState);
        LOGGER.debug("Updated state for player {}", playerUuid);
    }

    /**
     * 获取所有在线玩家状态
     */
    public java.util.Collection<PlayerState> getAllPlayerStates() {
        return playerStates.values();
    }

    public void shutdown() {
        playerStates.clear();
        LOGGER.info("Player state manager shut down");
    }
}
