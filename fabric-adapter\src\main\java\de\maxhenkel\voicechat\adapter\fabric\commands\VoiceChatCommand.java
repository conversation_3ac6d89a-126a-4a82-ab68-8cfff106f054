package de.maxhenkel.voicechat.adapter.fabric.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.text.Text;
import net.minecraft.server.network.ServerPlayerEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 语音聊天命令 - Fabric 版本
 * 完全对应 minecraft-adapter 的 VoiceChatCommand
 */
public class VoiceChatCommand {

    private static final Logger LOGGER = LoggerFactory.getLogger(VoiceChatCommand.class);

    private static FabricVoiceChatAdapter plugin;
    private static VoiceServerClient voiceServerClient;

    public static void register(FabricVoiceChatAdapter pluginInstance, VoiceServerClient client) {
        plugin = pluginInstance;
        voiceServerClient = client;

        CommandRegistrationCallback.EVENT.register((dispatcher, registryAccess, environment) -> {
            registerCommands(dispatcher);
        });

        LOGGER.info("Voice chat commands registered");
    }

    private static void registerCommands(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("voicechat")
            .executes(VoiceChatCommand::handleHelp)
            .then(Commands.literal("status")
                .requires(source -> source.hasPermission(4))
                .executes(VoiceChatCommand::handleStatus))
            .then(Commands.literal("reload")
                .requires(source -> source.hasPermission(4))
                .executes(VoiceChatCommand::handleReload))
            .then(Commands.literal("secret")
                .requires(source -> source.hasPermission(4))
                .executes(VoiceChatCommand::handleSecretSelf)
                .then(Commands.argument("player", EntityArgument.player())
                    .executes(VoiceChatCommand::handleSecretPlayer)))
            .then(Commands.literal("syncgroups")
                .requires(source -> source.hasPermission(4))
                .executes(VoiceChatCommand::handleSyncGroups))
            .then(Commands.literal("syncperms")
                .requires(source -> source.hasPermission(4))
                .executes(VoiceChatCommand::handleSyncPermsSelf)
                .then(Commands.argument("player", EntityArgument.player())
                    .executes(VoiceChatCommand::handleSyncPermsPlayer)))
            .then(Commands.literal("help")
                .executes(VoiceChatCommand::handleHelp))
        );
    }

    private static int handleHelp(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        source.sendSuccess(() -> Component.literal("§6=== Voice Chat Adapter Commands ==="), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat status§f - Show voice server status"), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat reload§f - Reload configuration"), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat secret [player]§f - Generate voice chat secret"), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat syncgroups§f - Manually sync voice chat groups"), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat syncperms [player]§f - Sync player permissions"), false);
        source.sendSuccess(() -> Component.literal("§e/voicechat help§f - Show this help message"), false);

        return 1;
    }

    private static int handleStatus(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        source.sendSuccess(() -> Component.literal("§eFetching voice server status..."), false);

        CompletableFuture.runAsync(() -> {
            try {
                Map<String, Object> status = voiceServerClient.getServerStatus();

                source.getServer().execute(() -> {
                    source.sendSuccess(() -> Component.literal("§a=== Voice Server Status ==="), false);
                    source.sendSuccess(() -> Component.literal("§fConnected: §a" + voiceServerClient.isConnected()), false);

                    if (status != null) {
                        source.sendSuccess(() -> Component.literal("§fOnline Players: §b" + status.getOrDefault("onlinePlayers", "Unknown")), false);
                        source.sendSuccess(() -> Component.literal("§fActive Groups: §b" + status.getOrDefault("activeGroups", "Unknown")), false);
                        source.sendSuccess(() -> Component.literal("§fServer Uptime: §b" + status.getOrDefault("uptime", "Unknown")), false);
                    }
                });

            } catch (Exception e) {
                LOGGER.error("Failed to get server status", e);
                source.getServer().execute(() -> {
                    source.sendFailure(Component.literal("§cFailed to get server status: " + e.getMessage()));
                });
            }
        });

        return 1;
    }

    private static int handleReload(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        source.sendSuccess(() -> Component.literal("§eReloading voice chat adapter configuration..."), false);

        CompletableFuture.runAsync(() -> {
            try {
                // TODO: 实现配置重载
                source.getServer().execute(() -> {
                    source.sendSuccess(() -> Component.literal("§aConfiguration reloaded successfully!"), false);
                });

            } catch (Exception e) {
                LOGGER.error("Failed to reload configuration", e);
                source.getServer().execute(() -> {
                    source.sendFailure(Component.literal("§cFailed to reload configuration: " + e.getMessage()));
                });
            }
        });

        return 1;
    }

    private static int handleSecretSelf(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        ServerPlayer player = source.getPlayerOrException();

        return generateSecret(source, player);
    }

    private static int handleSecretPlayer(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        ServerPlayer targetPlayer = EntityArgument.getPlayer(context, "player");

        return generateSecret(source, targetPlayer);
    }

    private static int generateSecret(CommandSourceStack source, ServerPlayer targetPlayer) {
        source.sendSuccess(() -> Component.literal("§eGenerating voice chat secret for " + targetPlayer.getName().getString() + "..."), false);

        CompletableFuture.runAsync(() -> {
            try {
                String secret = voiceServerClient.generatePlayerSecret(targetPlayer.getUUID());

                source.getServer().execute(() -> {
                    source.sendSuccess(() -> Component.literal("§aVoice chat secret for " + targetPlayer.getName().getString() + ": §f" + secret), false);

                    if (!source.getEntity().equals(targetPlayer)) {
                        targetPlayer.sendSystemMessage(Component.literal("§aYour voice chat secret: §f" + secret));
                    }
                });

            } catch (Exception e) {
                LOGGER.error("Failed to generate secret for {}", targetPlayer.getName().getString(), e);
                source.getServer().execute(() -> {
                    source.sendFailure(Component.literal("§cFailed to generate secret: " + e.getMessage()));
                });
            }
        });

        return 1;
    }

    private static int handleSyncGroups(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();

        source.sendSuccess(() -> Component.literal("§eSyncing voice chat groups..."), false);

        CompletableFuture.runAsync(() -> {
            try {
                // 重新同步所有在线玩家的群组信息
                for (ServerPlayer player : source.getServer().getPlayerList().getPlayers()) {
                    plugin.getGroupMessageHandler().syncGroupsForPlayer(player);
                }

                source.getServer().execute(() -> {
                    source.sendSuccess(() -> Component.literal("§aVoice chat groups synced successfully!"), false);
                });

            } catch (Exception e) {
                LOGGER.error("Failed to sync groups", e);
                source.getServer().execute(() -> {
                    source.sendFailure(Component.literal("§cFailed to sync groups: " + e.getMessage()));
                });
            }
        });

        return 1;
    }

    private static int handleSyncPermsSelf(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        ServerPlayer player = source.getPlayerOrException();

        return syncPermissions(source, player);
    }

    private static int handleSyncPermsPlayer(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        ServerPlayer targetPlayer = EntityArgument.getPlayer(context, "player");

        return syncPermissions(source, targetPlayer);
    }

    private static int syncPermissions(CommandSourceStack source, ServerPlayer targetPlayer) {
        source.sendSuccess(() -> Component.literal("§eSyncing permissions for " + targetPlayer.getName().getString() + "..."), false);

        CompletableFuture.runAsync(() -> {
            try {
                plugin.getPermissionSyncManager().syncPlayerPermissions(targetPlayer);

                source.getServer().execute(() -> {
                    source.sendSuccess(() -> Component.literal("§aPermissions synced for " + targetPlayer.getName().getString() + "!"), false);
                });

            } catch (Exception e) {
                LOGGER.error("Failed to sync permissions for {}", targetPlayer.getName().getString(), e);
                source.getServer().execute(() -> {
                    source.sendFailure(Component.literal("§cFailed to sync permissions: " + e.getMessage()));
                });
            }
        });

        return 1;
    }
}
