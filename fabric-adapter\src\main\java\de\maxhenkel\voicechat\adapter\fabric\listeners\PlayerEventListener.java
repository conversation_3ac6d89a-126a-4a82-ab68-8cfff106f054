package de.maxhenkel.voicechat.adapter.fabric.listeners;

import de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter;
import de.maxhenkel.voicechat.adapter.fabric.managers.PlayerStateManager;
import de.maxhenkel.voicechat.adapter.fabric.model.PlayerInfo;
import de.maxhenkel.voicechat.adapter.fabric.model.Position;
import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import de.maxhenkel.voicechat.adapter.fabric.network.GroupMessageHandler;
import de.maxhenkel.voicechat.adapter.fabric.permission.PermissionSyncManager;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.world.GameMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 玩家事件监听器 - Fabric 版本
 * 完全对应 minecraft-adapter 的 PlayerEventListener
 */
public class PlayerEventListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerEventListener.class);

    private final FabricVoiceChatAdapter plugin;
    private final VoiceServerClient voiceServerClient;
    private final PlayerStateManager playerStateManager;
    private final ConcurrentHashMap<ServerPlayerEntity, Position> lastPositions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    public PlayerEventListener(FabricVoiceChatAdapter plugin, VoiceServerClient voiceServerClient) {
        this.plugin = plugin;
        this.voiceServerClient = voiceServerClient;
        this.playerStateManager = plugin.getPlayerStateManager();

        // 启动定时同步任务
        startSyncTasks();

        LOGGER.info("Player event listener initialized");
    }

    /**
     * 玩家加入事件
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        // 异步处理玩家登录
        scheduler.execute(() -> {
            try {
                PlayerInfo playerInfo = createPlayerInfo(player);
                voiceServerClient.playerLogin(playerInfo);

                // 在PlayerStateManager中注册玩家
                playerStateManager.onPlayerJoin(player);

                // 同步玩家权限
                PermissionSyncManager permissionSyncManager = plugin.getPermissionSyncManager();
                if (permissionSyncManager != null) {
                    permissionSyncManager.syncPlayerPermissions(player);
                }

                // 记录初始位置
                lastPositions.put(player, playerInfo.getPosition());

                // 延迟同步群组，确保语音聊天连接已建立
                scheduler.schedule(() -> {
                    GroupMessageHandler groupHandler = plugin.getGroupMessageHandler();
                    if (groupHandler != null) {
                        groupHandler.syncGroupsForPlayer(player);
                    }

                    // 发送所有玩家状态给新加入的玩家
                    playerStateManager.sendAllStatesToPlayer(player);
                }, 3, TimeUnit.SECONDS); // 3秒后同步群组

                LOGGER.debug("Player {} joined and synced to voice server", player.getName().getString());

            } catch (Exception e) {
                LOGGER.error("Failed to sync player join for {}", player.getName().getString(), e);
            }
        });
    }

    /**
     * 玩家退出事件
     */
    public void onPlayerQuit(ServerPlayerEntity player) {
        // 异步处理玩家登出
        scheduler.execute(() -> {
            try {
                voiceServerClient.playerLogout(player.getUuid());

                // 在PlayerStateManager中注销玩家
                playerStateManager.onPlayerQuit(player);

                // 清理权限缓存
                PermissionSyncManager permissionSyncManager = plugin.getPermissionSyncManager();
                if (permissionSyncManager != null) {
                    permissionSyncManager.onPlayerDisconnect(player.getUuid());
                }

                lastPositions.remove(player);

                LOGGER.debug("Player {} quit and synced to voice server", player.getName().getString());

            } catch (Exception e) {
                LOGGER.error("Failed to sync player quit for {}", player.getName().getString(), e);
            }
        });
    }

    /**
     * 玩家移动事件处理
     */
    public void onPlayerMove(ServerPlayerEntity player, Position newPosition) {
        if (!plugin.getAdapterConfig().getSync().isSyncOnMove()) {
            return;
        }

        Position lastPosition = lastPositions.get(player);

        // 检查是否移动了足够的距离
        if (lastPosition != null) {
            double distance = newPosition.getDistanceTo(lastPosition);
            if (distance < plugin.getAdapterConfig().getSync().getMinMoveDistance()) {
                return;
            }
        }

        // 异步更新位置
        scheduler.execute(() -> {
            try {
                voiceServerClient.updatePlayerPosition(player.getUuid(), newPosition);
                lastPositions.put(player, newPosition);

            } catch (Exception e) {
                LOGGER.warn("Failed to sync position for {}", player.getName().getString(), e);
            }
        });
    }

    /**
     * 玩家游戏模式变更事件
     */
    public void onPlayerGameModeChange(ServerPlayerEntity player, GameMode newGameMode) {
        int gameMode = convertGameMode(newGameMode);

        // 异步更新游戏模式
        scheduler.execute(() -> {
            try {
                voiceServerClient.updatePlayerGameMode(player.getUuid(), gameMode);
                LOGGER.debug("Updated game mode for {}: -> {}",
                           player.getName().getString(), newGameMode);
            } catch (Exception e) {
                LOGGER.warn("Failed to sync game mode for {}", player.getName().getString(), e);
            }
        });
    }

    /**
     * 玩家传送事件
     */
    public void onPlayerTeleport(ServerPlayerEntity player, Position newPosition) {
        // 异步更新位置
        scheduler.execute(() -> {
            try {
                voiceServerClient.updatePlayerPosition(player.getUuid(), newPosition);
                lastPositions.put(player, newPosition);

            } catch (Exception e) {
                LOGGER.warn("Failed to sync teleport for {}", player.getName().getString(), e);
            }
        });
    }

    /**
     * 启动定时同步任务
     */
    private void startSyncTasks() {
        // 位置同步任务
        int positionInterval = plugin.getAdapterConfig().getSync().getPositionSyncInterval();
        scheduler.scheduleAtFixedRate(this::syncAllPlayerPositions,
                                    positionInterval, positionInterval, TimeUnit.MILLISECONDS);

        // 权限同步任务
        int permissionInterval = plugin.getAdapterConfig().getSync().getPermissionSyncInterval();
        scheduler.scheduleAtFixedRate(this::syncAllPlayerPermissions,
                                    permissionInterval, permissionInterval, TimeUnit.MILLISECONDS);

        // 服务器重启检查任务（每30秒检查一次）
        scheduler.scheduleAtFixedRate(this::checkServerRestart, 30, 30, TimeUnit.SECONDS);

        LOGGER.info("Started sync tasks - position: {}ms, permission: {}ms",
                   positionInterval, permissionInterval);
    }

    /**
     * 同步所有玩家位置
     */
    private void syncAllPlayerPositions() {
        if (plugin.getServer() == null) return;

        for (ServerPlayerEntity player : plugin.getServer().getPlayerManager().getPlayerList()) {
            try {
                Position currentPosition = createPosition(player);
                Position lastPosition = lastPositions.get(player);

                // 检查位置是否有变化
                if (lastPosition == null || !currentPosition.equals(lastPosition)) {
                    voiceServerClient.updatePlayerPosition(player.getUuid(), currentPosition);
                    lastPositions.put(player, currentPosition);
                }

            } catch (Exception e) {
                LOGGER.warn("Failed to sync position for {}", player.getName().getString(), e);
            }
        }
    }

    /**
     * 同步所有玩家权限
     */
    private void syncAllPlayerPermissions() {
        if (plugin.getServer() == null) return;

        for (ServerPlayerEntity player : plugin.getServer().getPlayerManager().getPlayerList()) {
            try {
                List<String> permissionList = getPlayerPermissions(player);
                Map<String, Boolean> permissions = new HashMap<>();
                for (String permission : permissionList) {
                    permissions.put(permission, true);
                }
                voiceServerClient.updatePlayerPermissions(player.getUuid(), permissions);

            } catch (Exception e) {
                LOGGER.warn("Failed to sync permissions for {}", player.getName().getString(), e);
            }
        }
    }

    /**
     * 检查服务器重启并重新同步玩家
     */
    private void checkServerRestart() {
        try {
            if (voiceServerClient.checkServerRestart()) {
                LOGGER.info("Voice server restart detected, resyncing all online players");

                // 重新同步所有在线玩家
                if (plugin.getServer() != null) {
                    voiceServerClient.resyncAllPlayers(plugin.getServer().getPlayerManager().getPlayerList());
                }

                // 清空本地位置缓存，强制重新同步位置
                lastPositions.clear();

                LOGGER.info("Completed resyncing after voice server restart");
            }
        } catch (Exception e) {
            LOGGER.error("Error checking server restart: {}", e.getMessage());
        }
    }

    /**
     * 创建玩家信息
     */
    private PlayerInfo createPlayerInfo(ServerPlayerEntity player) {
        PlayerInfo playerInfo = new PlayerInfo(player.getUuid(), player.getName().getString());
        playerInfo.setPosition(createPosition(player));
        playerInfo.setPermissions(getPlayerPermissions(player));
        playerInfo.setGameMode(convertGameMode(player.interactionManager.getGameMode()));
        return playerInfo;
    }

    /**
     * 创建位置信息
     */
    private Position createPosition(ServerPlayerEntity player) {
        return new Position(
                createWorldId(player.getWorld().getRegistryKey().getValue().toString()),
                player.getX(),
                player.getY(),
                player.getZ()
        );
    }

    /**
     * 创建带服务器前缀的世界ID，避免跨服务器世界名冲突
     */
    private String createWorldId(String worldName) {
        String serverName = plugin.getAdapterConfig().getServerName();
        return serverName + ":" + worldName;
    }

    /**
     * 获取玩家权限列表
     */
    private List<String> getPlayerPermissions(ServerPlayerEntity player) {
        List<String> permissions = new ArrayList<>();

        // TODO: 实现 Fabric 权限系统集成
        // 暂时添加基本权限
        permissions.add("voicechat.speak");
        permissions.add("voicechat.listen");
        permissions.add("voicechat.groups");

        // 检查管理员权限
        if (player.hasPermissionLevel(4)) {
            permissions.add("voicechat.admin");
            permissions.add("voicechat.record");
        }

        return permissions;
    }

    /**
     * 转换游戏模式
     */
    private int convertGameMode(GameMode gameMode) {
        return switch (gameMode) {
            case SURVIVAL -> 0;
            case CREATIVE -> 1;
            case ADVENTURE -> 2;
            case SPECTATOR -> 3;
        };
    }

    /**
     * 关闭事件监听器
     */
    public void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        lastPositions.clear();
        LOGGER.info("Player event listener shut down");
    }
}
