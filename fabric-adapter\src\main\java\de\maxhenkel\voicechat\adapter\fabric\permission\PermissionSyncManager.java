package de.maxhenkel.voicechat.adapter.fabric.permission;

import de.maxhenkel.voicechat.adapter.fabric.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import net.minecraft.server.network.ServerPlayerEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 权限同步管理器 - Fabric 版本
 * 完全对应 minecraft-adapter 的 PermissionSyncManager
 */
public class PermissionSyncManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionSyncManager.class);

    private final VoiceServerClient voiceServerClient;
    private final AdapterConfig config;
    private final ConcurrentHashMap<UUID, Map<String, Boolean>> permissionCache = new ConcurrentHashMap<>();

    public PermissionSyncManager(VoiceServerClient voiceServerClient, AdapterConfig config) {
        this.voiceServerClient = voiceServerClient;
        this.config = config;

        LOGGER.info("Permission sync manager initialized");
    }

    /**
     * 同步玩家权限到语音服务器
     */
    public void syncPlayerPermissions(ServerPlayerEntity player) {
        try {
            Map<String, Boolean> permissions = getPlayerPermissions(player);
            voiceServerClient.updatePlayerPermissions(player.getUuid(), permissions);

            // 缓存权限
            permissionCache.put(player.getUuid(), permissions);

            LOGGER.debug("Synced permissions for player {}: {}", player.getName().getString(), permissions);
        } catch (Exception e) {
            LOGGER.error("Failed to sync permissions for player {}", player.getName().getString(), e);
        }
    }

    /**
     * 玩家断开连接时清理缓存
     */
    public void onPlayerDisconnect(UUID playerUuid) {
        permissionCache.remove(playerUuid);
        LOGGER.debug("Cleared permission cache for player {}", playerUuid);
    }

    /**
     * 获取玩家权限
     */
    private Map<String, Boolean> getPlayerPermissions(ServerPlayerEntity player) {
        Map<String, Boolean> permissions = new HashMap<>();

        // 基于配置的默认权限
        AdapterConfig.PermissionConfig defaultPerms = config.getPermissions();
        permissions.put("voicechat.listen", defaultPerms.isListen());
        permissions.put("voicechat.speak", defaultPerms.isSpeak());
        permissions.put("voicechat.groups", defaultPerms.isGroups());
        permissions.put("voicechat.create_group", defaultPerms.isCreateGroup());
        permissions.put("voicechat.join_group", defaultPerms.isJoinGroup());
        permissions.put("voicechat.cross_world", defaultPerms.isCrossWorld());
        permissions.put("voicechat.cross_server", defaultPerms.isCrossServer());
        permissions.put("voicechat.record", defaultPerms.isRecord());
        permissions.put("voicechat.admin", defaultPerms.isAdmin());

        // TODO: 集成 Fabric 权限系统
        // 检查管理员权限
        if (player.hasPermissionLevel(4)) {
            permissions.put("voicechat.admin", true);
            permissions.put("voicechat.record", true);
        }

        return permissions;
    }

    /**
     * 检查玩家是否有指定权限
     */
    public boolean hasPermission(ServerPlayerEntity player, String permission) {
        Map<String, Boolean> permissions = permissionCache.get(player.getUuid());
        if (permissions != null) {
            return permissions.getOrDefault(permission, false);
        }

        // 如果缓存中没有，重新获取
        permissions = getPlayerPermissions(player);
        permissionCache.put(player.getUuid(), permissions);
        return permissions.getOrDefault(permission, false);
    }

    /**
     * 获取缓存的权限
     */
    public Map<String, Boolean> getCachedPermissions(UUID playerUuid) {
        return permissionCache.get(playerUuid);
    }

    public void shutdown() {
        permissionCache.clear();
        LOGGER.info("Permission sync manager shut down");
    }
}
