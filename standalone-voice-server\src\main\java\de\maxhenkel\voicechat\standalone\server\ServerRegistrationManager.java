package de.maxhenkel.voicechat.standalone.server;

import de.maxhenkel.voicechat.standalone.model.PlayerData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 管理Minecraft Adapter服务器的注册、keepalive和超时清理
 */
public class ServerRegistrationManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ServerRegistrationManager.class);
    
    // 服务器注册信息存储
    private final Map<String, RegisteredServer> registeredServers = new ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // 依赖的管理器
    private final VoiceServer voiceServer;
    private final PermissionManager permissionManager;
    private final WorldManager worldManager;
    
    // 配置常量
    private static final long KEEPALIVE_TIMEOUT_MS = 30 * 60 * 1000; // 30分钟超时
    private static final long CLEANUP_INTERVAL_MS = 30 * 60 * 1000; // 30分钟清理间隔

    public ServerRegistrationManager(VoiceServer voiceServer, PermissionManager permissionManager, WorldManager worldManager) {
        this.voiceServer = voiceServer;
        this.permissionManager = permissionManager;
        this.worldManager = worldManager;
    }
    
    /**
     * 注册的服务器信息
     */
    public static class RegisteredServer {
        private final String serverName;
        private final String host;
        private final int port;
        private final long registrationTime;
        private volatile long lastKeepaliveTime;
        
        public RegisteredServer(String serverName, String host, int port) {
            this.serverName = serverName;
            this.host = host;
            this.port = port;
            this.registrationTime = System.currentTimeMillis();
            this.lastKeepaliveTime = System.currentTimeMillis();
        }
        
        public String getServerName() { return serverName; }
        public String getHost() { return host; }
        public int getPort() { return port; }
        public long getRegistrationTime() { return registrationTime; }
        public long getLastKeepaliveTime() { return lastKeepaliveTime; }
        
        public void updateKeepalive() {
            this.lastKeepaliveTime = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - lastKeepaliveTime > KEEPALIVE_TIMEOUT_MS;
        }
        
        @Override
        public String toString() {
            return String.format("RegisteredServer{name='%s', host='%s', port=%d, lastKeepalive=%d}", 
                    serverName, host, port, lastKeepaliveTime);
        }
    }
    
    /**
     * 启动服务器注册管理器
     */
    public void start() {
        LOGGER.info("Starting server registration manager");
        
        // 启动定时清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredServers, 
                CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        LOGGER.info("Server registration manager started with cleanup interval: {} minutes", 
                CLEANUP_INTERVAL_MS / 60000);
    }
    
    /**
     * 停止服务器注册管理器
     */
    public void stop() {
        LOGGER.info("Stopping server registration manager");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        registeredServers.clear();
        LOGGER.info("Server registration manager stopped");
    }
    
    /**
     * 注册服务器
     * @param serverName 服务器名称
     * @param host 服务器主机地址
     * @param port 服务器端口
     * @return 如果注册成功返回true，如果服务器名称已存在返回false
     */
    public boolean registerServer(String serverName, String host, int port) {
        if (serverName == null || serverName.trim().isEmpty()) {
            LOGGER.warn("Attempted to register server with empty name");
            return false;
        }

        boolean isReregistration = registeredServers.containsKey(serverName);

        RegisteredServer server = new RegisteredServer(serverName, host, port);
        registeredServers.put(serverName, server);

        // 自动配置跨服务器通信
        configureServerForCrossCommunication(serverName);

        if (isReregistration) {
            LOGGER.info("Server '{}' re-registered successfully from {}:{}", serverName, host, port);
        } else {
            LOGGER.info("Server '{}' registered successfully from {}:{}", serverName, host, port);
            // 新服务器注册时，立即为所有现有服务器建立跨服务器规则
            establishCrossServerRulesForAllServers();
        }

        return true;
    }
    
    /**
     * 更新服务器keepalive
     * @param serverName 服务器名称
     * @return 如果服务器存在并更新成功返回true，否则返回false
     */
    public boolean updateKeepalive(String serverName) {
        RegisteredServer server = registeredServers.get(serverName);
        if (server == null) {
            LOGGER.warn("Attempted to update keepalive for unregistered server: {}", serverName);
            return false;
        }
        
        server.updateKeepalive();
        LOGGER.debug("Updated keepalive for server: {}", serverName);
        return true;
    }
    
    /**
     * 取消注册服务器
     * @param serverName 服务器名称
     * @return 如果服务器存在并取消注册成功返回true，否则返回false
     */
    public boolean unregisterServer(String serverName) {
        RegisteredServer server = registeredServers.remove(serverName);
        if (server != null) {
            // 清理该服务器上的所有玩家
            cleanupServerPlayers(serverName);

            // 取消跨服务器通信配置
            unconfigureServerForCrossCommunication(serverName);

            LOGGER.info("Server '{}' unregistered and all players cleaned up", serverName);
            return true;
        } else {
            LOGGER.warn("Attempted to unregister non-existent server: {}", serverName);
            return false;
        }
    }
    
    /**
     * 检查服务器名称是否已被注册
     * @param serverName 服务器名称
     * @return 如果已注册返回true，否则返回false
     */
    public boolean isServerRegistered(String serverName) {
        return registeredServers.containsKey(serverName);
    }
    
    /**
     * 获取注册的服务器信息
     * @param serverName 服务器名称
     * @return 服务器信息，如果不存在返回null
     */
    public RegisteredServer getRegisteredServer(String serverName) {
        return registeredServers.get(serverName);
    }
    
    /**
     * 获取所有注册的服务器
     * @return 所有注册的服务器信息
     */
    public Map<String, RegisteredServer> getAllRegisteredServers() {
        return Map.copyOf(registeredServers);
    }
    
    /**
     * 获取活跃的服务器列表（未过期的）
     * @return 活跃的服务器名称集合
     */
    public Set<String> getActiveServerNames() {
        return registeredServers.entrySet().stream()
                .filter(entry -> !entry.getValue().isExpired())
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }
    
    /**
     * 清理过期的服务器
     */
    private void cleanupExpiredServers() {
        long currentTime = System.currentTimeMillis();
        int removedCount = 0;

        var iterator = registeredServers.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            RegisteredServer server = entry.getValue();

            if (server.isExpired()) {
                String serverName = server.getServerName();

                // 清理该服务器上的所有玩家
                cleanupServerPlayers(serverName);

                // 取消跨服务器通信配置
                unconfigureServerForCrossCommunication(serverName);

                iterator.remove();
                removedCount++;
                LOGGER.info("Removed expired server '{}' (last keepalive: {} minutes ago)",
                        serverName,
                        (currentTime - server.getLastKeepaliveTime()) / 60000);
            }
        }

        if (removedCount > 0) {
            LOGGER.info("Cleanup completed: removed {} expired servers", removedCount);
        } else {
            LOGGER.debug("Cleanup completed: no expired servers found");
        }
    }
    
    /**
     * 获取统计信息
     * @return 包含统计信息的Map
     */
    public Map<String, Object> getStatistics() {
        long currentTime = System.currentTimeMillis();
        int totalServers = registeredServers.size();
        int activeServers = (int) registeredServers.values().stream()
                .filter(server -> !server.isExpired())
                .count();
        int expiredServers = totalServers - activeServers;
        
        return Map.of(
                "totalServers", totalServers,
                "activeServers", activeServers,
                "expiredServers", expiredServers,
                "keepaliveTimeoutMinutes", KEEPALIVE_TIMEOUT_MS / 60000,
                "cleanupIntervalMinutes", CLEANUP_INTERVAL_MS / 60000
        );
    }

    /**
     * 配置服务器以支持跨服务器通信
     */
    private void configureServerForCrossCommunication(String serverName) {
        try {
            // 1. 确保服务器配置存在并启用跨服务器通信
            WorldManager.ServerConfig serverConfig = worldManager.getServerConfig(serverName);
            if (serverConfig == null) {
                serverConfig = new WorldManager.ServerConfig(serverName);
                worldManager.setServerConfig(serverConfig);
            }

            // 启用跨服务器通信
            if (!serverConfig.isCrossServerAllowed()) {
                serverConfig.setCrossServerAllowed(true);
                LOGGER.info("Enabled cross-server communication for server: {}", serverName);
            }

            // 2. 为所有已注册的服务器配置双向跨服务器规则
            for (String existingServerName : registeredServers.keySet()) {
                if (!existingServerName.equals(serverName)) {
                    // 添加双向规则
                    worldManager.addCrossServerRule(serverName, existingServerName);
                    worldManager.addCrossServerRule(existingServerName, serverName);

                    // 确保现有服务器也启用跨服务器通信
                    WorldManager.ServerConfig existingServerConfig = worldManager.getServerConfig(existingServerName);
                    if (existingServerConfig == null) {
                        existingServerConfig = new WorldManager.ServerConfig(existingServerName);
                        worldManager.setServerConfig(existingServerConfig);
                    }
                    if (!existingServerConfig.isCrossServerAllowed()) {
                        existingServerConfig.setCrossServerAllowed(true);
                        LOGGER.info("Enabled cross-server communication for existing server: {}", existingServerName);
                    }
                }
            }

            // 3. 为所有玩家启用跨服务器权限（可选，根据需要调整）
            // 注意：这里我们设置默认权限，实际权限仍然可以通过API单独管理
            permissionManager.setDefaultPermission(de.maxhenkel.voicechat.standalone.model.Permission.CROSS_SERVER, true);
            LOGGER.info("Enabled default cross-server permission for all players");

        } catch (Exception e) {
            LOGGER.error("Failed to configure cross-server communication for server: {}", serverName, e);
        }
    }

    /**
     * 为所有已注册的服务器建立跨服务器规则
     * 这个方法确保所有服务器之间都有双向通信规则
     */
    private void establishCrossServerRulesForAllServers() {
        try {
            Set<String> serverNames = new HashSet<>(registeredServers.keySet());
            int rulesCreated = 0;

            // 为每对服务器建立双向规则
            for (String serverA : serverNames) {
                for (String serverB : serverNames) {
                    if (!serverA.equals(serverB)) {
                        // 检查规则是否已存在，避免重复创建
                        if (!worldManager.hasCrossServerRule(serverA, serverB)) {
                            worldManager.addCrossServerRule(serverA, serverB);
                            rulesCreated++;
                        }
                    }
                }
            }

            if (rulesCreated > 0) {
                LOGGER.info("Established {} cross-server rules for {} servers", rulesCreated, serverNames.size());
            }

        } catch (Exception e) {
            LOGGER.error("Failed to establish cross-server rules for all servers", e);
        }
    }

    /**
     * 清理指定服务器上的所有玩家
     */
    private void cleanupServerPlayers(String serverName) {
        try {
            PlayerManager playerManager = voiceServer.getPlayerManager();
            GroupManager groupManager = voiceServer.getGroupManager();

            // 获取该服务器上的所有玩家
            Collection<PlayerData> allPlayers = playerManager.getAllPlayers();
            List<PlayerData> serverPlayers = allPlayers.stream()
                    .filter(player -> serverName.equals(player.getServerName()))
                    .collect(Collectors.toList());

            if (serverPlayers.isEmpty()) {
                LOGGER.debug("No players found on server '{}' to cleanup", serverName);
                return;
            }

            int cleanedCount = 0;
            for (PlayerData player : serverPlayers) {
                UUID playerUuid = player.getUuid();

                // 断开语音连接
                voiceServer.disconnectPlayer(playerUuid);

                // 从群组中移除玩家
                if (player.hasGroup()) {
                    groupManager.leaveGroup(player.getGroupId(), playerUuid);
                }

                // 设置玩家离线
                playerManager.setPlayerOnline(playerUuid, false);

                cleanedCount++;
                LOGGER.debug("Cleaned up player {} from server '{}'", player.getName(), serverName);
            }

            LOGGER.info("Cleaned up {} players from server '{}'", cleanedCount, serverName);

        } catch (Exception e) {
            LOGGER.error("Failed to cleanup players for server: {}", serverName, e);
        }
    }

    /**
     * 取消服务器的跨服务器通信配置
     */
    private void unconfigureServerForCrossCommunication(String serverName) {
        try {
            // 移除与其他服务器的跨服务器规则
            for (String existingServerName : registeredServers.keySet()) {
                if (!existingServerName.equals(serverName)) {
                    worldManager.removeCrossServerRule(serverName, existingServerName);
                    worldManager.removeCrossServerRule(existingServerName, serverName);
                }
            }

            LOGGER.info("Removed cross-server rules for server: {}", serverName);

        } catch (Exception e) {
            LOGGER.error("Failed to unconfigure cross-server communication for server: {}", serverName, e);
        }
    }
}
