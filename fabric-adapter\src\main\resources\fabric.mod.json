{"schemaVersion": 1, "id": "fabric-voice-adapter", "version": "${version}", "name": "<PERSON>abric <PERSON> Chat <PERSON>", "description": "Fabric adapter for Simple Voice Chat standalone server integration", "authors": ["<PERSON>"], "contact": {"homepage": "https://modrepo.de/minecraft/voicechat/overview", "issues": "https://github.com/henkelmax/simple-voice-chat/issues", "sources": "https://github.com/henkelmax/simple-voice-chat", "discord": "https://discord.gg/4dH2zwTmyX"}, "license": "All Rights Reserved", "icon": "icon.png", "environment": "server", "entrypoints": {"main": ["de.maxhenkel.voicechat.adapter.fabric.FabricVoiceChatAdapter"]}, "depends": {"fabricloader": ">=${fabric_loader_version}", "minecraft": "${minecraft_version}", "java": ">=17", "fabric-api": ">=${fabric_api_version}"}, "suggests": {"fabric-permissions-api-v0": "*"}, "custom": {"modmenu": {"links": {"modmenu.discord": "https://discord.gg/4dH2zwTmyX"}}}}