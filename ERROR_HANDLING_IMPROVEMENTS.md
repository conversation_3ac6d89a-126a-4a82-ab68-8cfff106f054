# 错误处理改进

## 问题描述

在语音服务器重启或网络不稳定时，会出现以下两个主要问题：

1. **玩家登出404错误**：玩家退出时向语音服务器发送登出请求失败
2. **玩家登录时语音服务器未连接**：时序问题导致玩家登录时语音服务器尚未完全恢复

## 🔧 修复措施

### 修复1：优雅处理玩家登出404错误

**问题**：
```
[ERROR] Failed to sync player quit for YamabukiAlice
java.io.IOException: Player logout failed: 404 {"error":"Not Found","message":"The requested endpoint was not found"}
```

**解决方案**：
- 将404错误降级为DEBUG级别日志
- 区分不同类型的HTTP错误
- 确保本地清理逻辑不受影响

**代码改进**：
```java
try {
    voiceServerClient.playerLogout(player.getUniqueId());
    LOGGER.debug("Player {} logout synced to voice server", player.getName());
} catch (java.io.IOException e) {
    if (e.getMessage().contains("404") || e.getMessage().contains("Not Found")) {
        LOGGER.debug("Player {} was not found on voice server (already logged out), continuing cleanup", player.getName());
    } else {
        LOGGER.warn("Failed to sync player logout to voice server for {}: {}", player.getName(), e.getMessage());
    }
}
// 继续执行本地清理，不受影响
```

### 修复2：SecretPacketSender重试机制

**问题**：
```
[ERROR] Voice server is not connected, cannot send secret to YamabukiAlice
```

**解决方案**：
- 添加智能重试机制（最多3次，间隔1秒）
- 检查玩家在线状态，避免无效重试
- 提供用户友好的错误消息

**重试逻辑**：
```java
private void sendSecretWithRetry(Player player, int attempt) {
    final int MAX_RETRIES = 3;
    final long RETRY_DELAY_MS = 1000;
    
    // 检查玩家是否仍在线
    if (!player.isOnline()) {
        return;
    }
    
    // 检查连接状态并重试
    if (!voiceServerClient.isConnected()) {
        if (attempt < MAX_RETRIES) {
            // 延迟重试
            plugin.getServer().getScheduler().runTaskLaterAsynchronously(plugin, () -> {
                sendSecretWithRetry(player, attempt + 1);
            }, RETRY_DELAY_MS / 50);
            return;
        }
    }
    
    // 发送密钥
    sendSecretPacket(player);
}
```

### 修复3：防止重复重新注册

**问题**：
```
[WARN] Voice server restart detected! (重复出现)
[INFO] Triggering immediate re-registration for server: default (重复触发)
```

**解决方案**：
- 添加时间间隔检查，防止5秒内重复触发
- 记录上次重新注册时间

**防重复机制**：
```java
private volatile long lastReregistrationTime = 0;

private void triggerImmediateReregistration() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastReregistrationTime < 5000) {
        LOGGER.debug("Skipping re-registration, too soon since last attempt");
        return;
    }
    lastReregistrationTime = currentTime;
    // 执行重新注册...
}
```

## 📊 改进效果

### 错误处理改进

| 问题类型 | 修复前 | 修复后 |
|---------|--------|--------|
| 玩家登出404错误 | ERROR级别，影响后续处理 | DEBUG级别，不影响清理 |
| 密钥发送失败 | 立即失败，用户体验差 | 智能重试，成功率提高 |
| 重复重新注册 | 频繁触发，日志混乱 | 防重复机制，日志清晰 |

### 用户体验改进

**修复前**：
- 玩家退出时可能看到错误消息
- 语音连接经常失败，需要手动重连
- 服务器重启后恢复时间长

**修复后**：
- 玩家退出流畅，无错误提示
- 语音连接自动重试，成功率高
- 服务器重启后快速恢复（30秒-1分钟）

## 🔍 日志级别调整

### 正常情况日志
```
[DEBUG] Player YamabukiAlice logout synced to voice server
[INFO] Successfully sent secret to YamabukiAlice after 1 retries
[INFO] Successfully re-registered server after restart detection: default
```

### 异常情况日志
```
[DEBUG] Player YamabukiAlice was not found on voice server (already logged out), continuing cleanup
[WARN] Voice server is not connected, retrying secret request for YamabukiAlice (attempt 2/3)
[ERROR] Voice server is not connected after 3 attempts, cannot send secret to YamabukiAlice
```

## 🚀 最佳实践

### 1. 错误分类处理
- **预期错误**（如404）：DEBUG级别，不影响流程
- **网络错误**：WARN级别，尝试重试
- **系统错误**：ERROR级别，记录详细信息

### 2. 重试策略
- **最大重试次数**：3次（避免无限重试）
- **重试间隔**：1秒（平衡响应速度和服务器负载）
- **退避策略**：固定间隔（简单有效）

### 3. 用户体验
- **透明重试**：用户无感知的自动重试
- **友好提示**：失败时提供清晰的错误消息
- **状态检查**：避免对离线玩家的无效操作

## 📋 监控建议

### 关键指标
- **密钥发送成功率**：应 > 95%
- **重试次数分布**：大部分应在1-2次内成功
- **重新注册频率**：正常情况下应很少发生

### 告警设置
- 密钥发送失败率 > 10%
- 重新注册频率 > 1次/小时
- 404错误突然增加

通过这些改进，系统在面对网络不稳定和服务器重启时的鲁棒性大大增强，用户体验也得到显著提升。
