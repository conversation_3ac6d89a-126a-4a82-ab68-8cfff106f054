package de.maxhenkel.voicechat.adapter.fabric.world;

import de.maxhenkel.voicechat.adapter.fabric.config.AdapterConfig;
import de.maxhenkel.voicechat.adapter.fabric.network.VoiceServerClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 世界配置管理器 - Fabric 版本
 */
public class WorldConfigManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(WorldConfigManager.class);
    
    private final VoiceServerClient voiceServerClient;
    private final AdapterConfig config;
    
    public WorldConfigManager(VoiceServerClient voiceServerClient, AdapterConfig config) {
        this.voiceServerClient = voiceServerClient;
        this.config = config;
        
        LOGGER.info("World config manager initialized");
    }
}
